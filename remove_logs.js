const fs = require('fs');
const path = require('path');

// Read the file
const filePath = 'opendashboard-mono/apps/api/src/businessLogic/lead.ts';
const content = fs.readFileSync(filePath, 'utf8');

// Split into lines
const lines = content.split('\n');

// Filter out lines that contain console.log but keep console.error
const filteredLines = lines.filter(line => {
    // Keep the line if it doesn't contain console.log OR if it contains console.error
    return !line.includes('console.log') || line.includes('console.error');
});

// Join back and write to file
const newContent = filteredLines.join('\n');
fs.writeFileSync(filePath, newContent, 'utf8');

console.log('Successfully removed all console.log statements while preserving console.error statements');
