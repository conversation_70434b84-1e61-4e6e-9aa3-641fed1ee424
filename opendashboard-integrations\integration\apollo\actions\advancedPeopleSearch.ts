import { Action, Property, IntegrationContext } from '@opendashboard-inc/integration-core';

interface Input {
    searchParams: string;
    page?: string; // Add page parameter for pagination
}

interface Output {
    people: any[];
    totalCount: number;
    requestId: string;
    creditsUsed: number;
    processingTime: number;
}

export const advancedPeopleSearch: Action<Input, Output> = {
    identifier: 'advancedPeopleSearch',
    displayName: 'Advanced People Search',
    description: 'Search for people using Apollo API parameters. Apollo caps results at 100 per page and 5,000 total per search query (50 pages max).',
    intent: 'read',

    props: {
        searchParams: Property.LongText({
            displayName: 'Search Parameters (JSON)',
            description: 'Apollo API parameters (e.g., {"person_titles": ["Software Engineer"], "organization_industries": ["IT"]})',
            required: true,
        }),
        page: Property.ShortText({
            displayName: 'Page Number',
            description: 'Apollo page number (1, 2, 3, etc.) for pagination',
            required: false,
        }),
    },

    run: async (context: IntegrationContext<Input>): Promise<Output> => {
        const { auth, propsValue } = context;
        
        // Parse JSON inputs
        const searchParams: Record<string, any> = typeof propsValue.searchParams === 'string' 
            ? JSON.parse(propsValue.searchParams) 
            : propsValue.searchParams;
        const page = parseInt(propsValue.page || '1', 10); // Parse page parameter

        const startTime = Date.now();

        
        // Remove any api_key from searchParams to avoid sending it in the body
        const { api_key, ...cleanSearchParams } = searchParams;
        
        const apolloParams: any = {
            // Work with Apollo's natural pagination (typically ~100 results per page)
            per_page: 100,  // Apollo's natural page size
            page: page,      // Request specific page
            ...cleanSearchParams
        };



        const res = await fetch('https://api.apollo.io/api/v1/mixed_people/search', {
            method: 'POST',
            headers: {
                'X-Api-Key': auth.apiKey,
                'Content-Type': 'application/json',
            },
            body: JSON.stringify(apolloParams),
        });

        const data = await res.json();

        if (!res.ok) {
            throw new Error(`Apollo API error: ${res.status} ${res.statusText} - ${JSON.stringify(data)}`);
        }

        const processingTime = Date.now() - startTime;

        const people = data.people || [];
        const totalCount = data.pagination?.total_entries || people.length;

        return {
            people,
            totalCount: data.pagination?.total_entries || (data.people?.length ?? 0),
            requestId: data.request_id || `req_${Date.now()}`,
            creditsUsed: data.credits_used ?? 0,
            processingTime
        };
    },
};
