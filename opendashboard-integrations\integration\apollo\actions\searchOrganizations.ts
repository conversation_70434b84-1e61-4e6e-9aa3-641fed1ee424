import { Action, Property, IntegrationContext } from '@opendashboard-inc/integration-core';

interface Input {
  searchParams: string;
  page?: string; // Optional page parameter for pagination
}

interface Output {
  organizations: any[];
  totalCount: number;
  requestId: string;
  creditsUsed: number;
  processingTime: number;
}

export const searchOrganizations: Action<Input, Output> = {
  identifier: 'searchOrganizations',
  displayName: 'Search Organizations',
  description:
    'Search for companies/organizations using Apollo API parameters. Apollo caps results at 100 per page and 50,000 total per search query (500 pages max).',
  intent: 'read',

  props: {
    searchParams: Property.LongText({
      displayName: 'Search Parameters (JSON)',
      description:
        'Apollo API parameters (e.g., {"organization_industries": ["IT"], "organization_size_ranges": ["51-200"]})',
      required: true,
    }),
    page: Property.ShortText({
      displayName: 'Page Number',
      description: 'Apollo page number (1, 2, 3, etc.) for pagination',
      required: false,
    }),
  },

  run: async (context: IntegrationContext<Input>): Promise<Output> => {
    const { auth, propsValue } = context;

    // Parse JSON input safely
    const searchParams: Record<string, any> =
      typeof propsValue.searchParams === 'string'
        ? JSON.parse(propsValue.searchParams)
        : propsValue.searchParams;

    const page = parseInt(propsValue.page || '1', 10);

    const startTime = Date.now();

  
    const { api_key, ...cleanSearchParams } = searchParams;
    
    const apolloParams = {
      per_page: 100, 
      page,
      ...cleanSearchParams,
    };



    const res = await fetch('https://api.apollo.io/api/v1/mixed_companies/search', {
      method: 'POST',
      headers: {
        'X-Api-Key': auth.apiKey,
        'Content-Type': 'application/json',
      },
      body: JSON.stringify(apolloParams),
    });

    
    const data = await res.json();

    if (!res.ok) {
      throw new Error(
        `Apollo API error: ${res.status} ${res.statusText} - ${JSON.stringify(data)}`
      );
    }

    const processingTime = Date.now() - startTime;

    // Apollo company search response structure
    const organizations = data.organizations || [];
    const totalCount = data.pagination?.total_entries || organizations.length;

    return {
      organizations,
      totalCount,
      requestId: data.request_id || data.requestId || `req_${Date.now()}`,
      creditsUsed: (data.credits_used || data.creditsUsed) ?? 0,
      processingTime,
    };
  },
};
