import { Lead, LeadType, LeadSource, NormalizedLeadData, ApolloPersonData, ApolloCompanyData } from "../entity/Lead";
import { LeadSearch, SearchFilters, SearchPagination, SearchResultMetadata } from "../entity/LeadSearch";
import { LeadUnlock, UnlockType, UnlockSource, UnlockMetadata } from "../entity/LeadUnlock";
import { ExecuteWorkspaceIntegrationActionNoAuth, GetMyWorkspace, HasPermission, WorkspacePermission } from "./workspace";
import { NotfoundError, RequiredParameterError, ErrorMessage, UnauthorizedError, BadRequestError, ServerProcessingError, InvalidParameterError } from "../errors/AppError";
import { createHash } from "crypto";
import { LeadService } from "../service/lead";
import { LeadSearchService } from "../service/leadSearch";
import { LeadUnlockService } from "../service/leadUnlock";
import { dbDataSource } from "../connection/db";
import { In, Not } from "typeorm";
import { GetMyDatabases, CreateDatabase, AddRecords, GetMyDatabase } from "./database";
import { GetWorkflows, CreateWorkflowInstance } from "./workflow";
import { SendEmail, SendEmailWithContent } from "./email";
import { OnDuplicateAction } from "../service/record";
import { RecordValues } from "@repo/app-db-utils/dist/typings/db";
import { consoleLog } from "./logtail";
import { ConnectionAPI, IntegrationCredentials } from '@opendashboard-inc/integration-core-api';

// Types for API interfaces
export interface SearchLeadsRequest {
    filters: SearchFilters;
    pagination: SearchPagination;
    excludeMyLeads?: boolean;
}

export interface LeadFilterOptions {
    page: number;
    limit: number;
    search?: string;
    filters?: Record<string, string | string[] | number | boolean>;
}

export interface SearchLeadsResponse {
    leads: Lead[];
    totalCount: number;
    hasNextPage: boolean;
    searchId: string;
    filters: SearchFilters;
    metadata: SearchResultMetadata;
}

export interface UnlockLeadRequest {
    unlockType: UnlockType;
    source: UnlockSource;
}

export interface SaveSearchRequest {
    name: string;
    description?: string;
    filters: SearchFilters;
    searchId?: string;
}

export interface LeadVoteRequest {
    vote: 'up' | 'down';
    feedback: string;
    feedbackType: 'predefined' | 'custom';
}

export interface LeadVoteFeedback {
    userId: string;
    vote: 'up' | 'down';
    feedback: string;
    feedbackType: 'predefined' | 'custom';
    votedAt: Date;
}

export interface LeadMeta {
    votes?: LeadVoteFeedback[];
    [key: string]: string | number | boolean | string[] | number[] | LeadVoteFeedback[] | undefined;
}

// Predefined feedback options for voting
export const PREDEFINED_FEEDBACK_OPTIONS = {
    up: [
        'High quality lead',
        'Good company fit',
        'Relevant job title',
        'Active on LinkedIn',
        'Company is growing',
        'Good location match',
        'Recent job change',
        'High engagement potential'
    ],
    down: [
        'Poor data quality',
        'Wrong company size',
        'Outdated information',
        'Low engagement potential',
        'Wrong location',
        'Company not relevant',
        'Job title mismatch',
        'Inactive profile'
    ]
} as const;


export const DEFAULT_PAGINATION = {
    PAGE: 1,
    LIMIT: 50,
    MAX_LIMIT: 50
} as const;



export interface BulkUnlockRequest {
    unlockType: UnlockType;
    source: UnlockSource;
}

export interface ExportRequest {
    format: 'csv' | 'json' | 'xlsx';
    includeFullData: boolean;
}

export interface LeadUpdateData {
    name?: string;
    email?: string;
    phone?: string;
    companyDomain?: string;
    isUnlocked?: boolean;
    lastEnrichedAt?: Date;
    meta?: Record<string, string | number | boolean | string[] | number[] | LeadVoteFeedback[]>;
}

export interface SearchUpdateData {
    name?: string;
    description?: string;
    filters?: SearchFilters;
    isSaved?: boolean;
}


export interface LeadResponse<T = any> {
    success: boolean;
    data: T;
    message?: string;
    error?: string;
}

// Pagination response interface (used in our functions)
export interface PaginatedResponse<T> {
    data: T[];
    totalCount: number;
    page: number;
    limit: number;
    hasNextPage: boolean;
    hasPreviousPage: boolean;
}


function generateSearchHash(filters: SearchFilters): string {
    // excludeMyLeads is NOT part of the search criteria - it's a display filter
    // Only the actual search filters should determine the hash
    // This ensures the same search hits cache regardless of excludeMyLeads value
    
    // Clean filters before hashing - remove excludeMyLeads
    const cleanFilters = { ...filters };
    delete cleanFilters.excludeMyLeads;
    
    const searchData = {
        filters: cleanFilters
       
    };
    
    const jsonString = JSON.stringify(searchData);
    const hash = createHash('sha256').update(jsonString).digest('hex');

    return hash;
}


function normalizeApolloPersonData(apolloData: ApolloPersonData): NormalizedLeadData {
    // Ensure we have required fields
    if (!apolloData.id) {
        throw new Error(`Missing required field: id=${apolloData.id}`);
    }

    return {
        id: apolloData.id,
        name: apolloData.name || `${apolloData.first_name || ''} ${apolloData.last_name || ''}`.trim(),
        email: apolloData.email,
        phone: apolloData.phone,
        jobTitle: apolloData.title,
        company: apolloData.organization?.name,
        companyDomain: apolloData.organization?.primary_domain,
        linkedinUrl: apolloData.linkedin_url,
        photoUrl: apolloData.photo_url,
        location: {
            country: apolloData.country,
            state: apolloData.state,
            city: apolloData.city
        },
        isEmailVisible: false,
        isPhoneVisible: false,
        confidence: apolloData.extrapolated_email_confidence
    };
}

// Helper function to normalize Apollo company data
function normalizeApolloCompanyData(apolloData: ApolloCompanyData): NormalizedLeadData {

    
    // Ensure we have required fields
    if (!apolloData.id || !apolloData.name) {
        throw new Error(`Missing required fields: id=${apolloData.id}, name=${apolloData.name}`);
    }
    
    return {
        id: apolloData.id,
        name: apolloData.name,
        email: undefined, // Companies don't have emails typically
        phone: apolloData.phone,
        jobTitle: undefined,
        company: apolloData.name,
        companyDomain: apolloData.primary_domain,
        linkedinUrl: apolloData.linkedin_url,
        photoUrl: apolloData.logo_url,
        location: undefined, // Companies might need different location handling
        isEmailVisible: false,
        isPhoneVisible: false,
        confidence: undefined
    };
}

// Search for people leads
export const SearchPeopleLeads = async (userId: string, workspaceId: string, request: SearchLeadsRequest): Promise<SearchLeadsResponse> => {
    validateWorkspaceId(workspaceId);
    validateSearchFilters(request.filters);
    validatePagination(request.pagination);
    
    return await SearchLeadsByType(userId, workspaceId, request, LeadType.Person);
};

// Search for company leads
export const SearchCompanyLeads = async (userId: string, workspaceId: string, request: SearchLeadsRequest): Promise<SearchLeadsResponse> => {
    validateWorkspaceId(workspaceId);
    validateSearchFilters(request.filters);
    validatePagination(request.pagination);
    
    return await SearchLeadsByType(userId, workspaceId, request, LeadType.Company);
};



// Internal search function that handles both types
async function SearchLeadsByType(userId: string, workspaceId: string, request: SearchLeadsRequest, leadType: LeadType): Promise<SearchLeadsResponse> {

    
    const member = await GetMyWorkspace(userId, workspaceId);
    if (!member) {
        throw new UnauthorizedError(ErrorMessage.UnableToAuthorize);
    }

    if (!HasPermission(member, WorkspacePermission.ReadWorkspace)) {
        throw new UnauthorizedError(ErrorMessage.UnableToAuthorize);
    }

    const { filters, pagination, excludeMyLeads } = request;
    
    // 🛡️ SAFETY NET STRATEGY: If bugs cause Apollo calls, we get different results
    // This ensures M1-M100 becomes M101-M200, expanding our database
    // Users never see duplicate results, even if system has bugs
    
    const searchHash = generateSearchHash(filters);
    
    const leadSearchService = new LeadSearchService();
    const leadService = new LeadService();

    let existingSearch = await leadSearchService.findByWorkspaceAndHash(workspaceId, searchHash);

    // If no existing search found, try to fix any corrupted searches
    if (!existingSearch) {
        // Try to fix any corrupted searches that might be causing cache issues
        try {
            const fixedCount = await leadSearchService.fixCorruptedSearches(workspaceId);
            if (fixedCount > 0) {
                // Try to find the search again after fixing
                const fixedSearch = await leadSearchService.findByWorkspaceAndHash(workspaceId, searchHash);
                if (fixedSearch && fixedSearch.lastExecutedAt) {
                    existingSearch = fixedSearch;
                }
            }
        } catch (error) {
            console.error(`🔧 [FIX DEBUG] Error fixing corrupted searches:`, error);
        }
    }
    
    if (!existingSearch) {
        // Check if we have an existing search and need to get more results
        let apolloPage = 1; // Default to page 1
        if (existingSearch && existingSearch.metadata?.apolloPage) {
            apolloPage = existingSearch.metadata.apolloPage + 1; // Get next page
        }

        const apolloSearchResults = await executeApolloSearch(workspaceId, filters, leadType, excludeMyLeads, apolloPage);
    }
    
    if (existingSearch && existingSearch.lastExecutedAt) {
        // Check if we need more results from Apollo (if user requests page beyond what we have)
        const maxResultsPerPage = 50;
        const totalPagesAvailable = Math.ceil((existingSearch.resultIds?.length || 0) / maxResultsPerPage);
        const requestedPage = pagination.page;
        
        if (requestedPage <= totalPagesAvailable) {
            // We have enough cached results for this page
            console.log(`🔍 [SEARCH DEBUG] ✅ Page ${requestedPage} available in cache`);
            console.log(`🔍 [SEARCH DEBUG] 📊 CACHE STATUS: ${existingSearch.resultIds?.length || 0} total leads cached`);
            
            // Get leads for the requested page from database
            const startIndex = (requestedPage - 1) * maxResultsPerPage;
            const endIndex = startIndex + maxResultsPerPage;
            const pageResultIds = existingSearch.resultIds?.slice(startIndex, endIndex) || [];
            
            console.log(`🔍 [SEARCH DEBUG] 📄 PAGINATION DETAILS:`);
            console.log(`🔍 [SEARCH DEBUG] - Requested Page: ${requestedPage}`);
            console.log(`🔍 [SEARCH DEBUG] - Requested Limit: ${pagination.limit}`);
            console.log(`🔍 [SEARCH DEBUG] - Lead IDs for this page: ${pageResultIds.length} IDs`);
            console.log(`🔍 [SEARCH DEBUG] - Start Index: ${startIndex}, End Index: ${endIndex}`);
            console.log(`🔍 [SEARCH DEBUG] ==========================================`);
            
            // Retrieve leads from database for this page
            const leadService = new LeadService();
            const leads = await leadService.findByWorkspaceAndIds(workspaceId, pageResultIds);
            console.log(`🔍 [SEARCH DEBUG] 📥 RETRIEVED LEADS FROM DATABASE:`);
            console.log(`🔍 [SEARCH DEBUG] - Retrieved: ${leads.length} leads`);
            console.log(`🔍 [SEARCH DEBUG] - Workspace ID: ${workspaceId}`);
            console.log(`🔍 [SEARCH DEBUG] - This ensures workspace isolation`);
        if (leads.length > 0) {
                const sampleNames = leads.slice(0, 3).map(lead => lead.name).join(', ');
                console.log(`🔍 [SEARCH DEBUG] - Sample lead names: ${sampleNames}`);
        }
            console.log(`🔍 [SEARCH DEBUG] ==========================================`);
        
            // Apply data privacy and exclude my leads
        const processedLeads = await processLeadsForResponse(leads, excludeMyLeads, userId, workspaceId);
        console.log(`🔍 [SEARCH DEBUG] After processing: ${processedLeads.length} leads`);
        if (processedLeads.length > 0) {
                const sampleNames = processedLeads.slice(0, 3).map(lead => lead.name).join(', ');
                console.log(`🔍 [SEARCH DEBUG] - Sample processed lead names: ${sampleNames}`);
        }
            console.log(`🔍 [SEARCH DEBUG] ==========================================`);
        
        console.log(`🔍 [SEARCH DEBUG] 🚫 Returning CACHED results from database (not calling Apollo)`);
        console.log(`🔍 [SEARCH DEBUG] 📊 SEARCH SUMMARY:`, {
            decision: 'CACHE_HIT',
            reason: 'Existing search found',
                searchHash: searchHash,
            existingSearchId: existingSearch.id,
            cachedResults: processedLeads.length,
                totalAvailable: existingSearch.totalCount,
                requestedPage: requestedPage,
                totalPagesAvailable: totalPagesAvailable,
                apolloPage: existingSearch.metadata?.apolloPage || 1,
                apolloTotalPages: existingSearch.metadata?.apolloTotalPages || 1
            });
            console.log(`🔍 [SEARCH DEBUG] ==========================================`);
            
            console.log(`🔍 [SEARCH DEBUG] 📊 PAGINATION CALCULATION (Cache Hit):`);
            console.log(`🔍 [SEARCH DEBUG] - Total Pages Available: ${totalPagesAvailable}`);
            console.log(`🔍 [SEARCH DEBUG] ==========================================`);
        
        return {
            leads: processedLeads,
            totalCount: existingSearch.totalCount,
                hasNextPage: requestedPage < totalPagesAvailable,
            searchId: existingSearch.id,
            filters: existingSearch.filters,
                metadata: {
                    apolloRequestId: existingSearch.metadata?.apolloRequestId,
                    apolloCreditsUsed: 0, // No credits used for cached results
                    processingTimeMs: 0,
                    resultQuality: 'high',
                    dataFreshness: existingSearch.lastExecutedAt || existingSearch.createdAt,
                    apolloPage: existingSearch.metadata?.apolloPage || 1,
                    apolloTotalPages: existingSearch.metadata?.apolloTotalPages || 1,
                    totalPagesAvailable: totalPagesAvailable // Add this to show frontend how many pages are cached
                }
            };
        } else {
            // User requested a page beyond what we have cached
            console.log(`🔍 [SEARCH DEBUG] ⚠️ Page ${requestedPage} requested but only ${totalPagesAvailable} pages available in cache`);
            console.log(`🔍 [SEARCH DEBUG] 🔄 Need to get more results from Apollo`);
            
            // Check if we can get more from Apollo
            const currentApolloPage = existingSearch.metadata?.apolloPage || 1;
            const apolloTotalPages = existingSearch.metadata?.apolloTotalPages || 1;
            
            console.log(`🔍 [SEARCH DEBUG] 📊 APOLLO PAGE STATUS:`);
            console.log(`🔍 [SEARCH DEBUG] - Current Apollo Page: ${currentApolloPage}`);
            console.log(`🔍 [SEARCH DEBUG] - Total Apollo Pages Available: ${apolloTotalPages}`);
            console.log(`🔍 [SEARCH DEBUG] - Can Get More: ${currentApolloPage < apolloTotalPages ? 'YES' : 'NO'}`);
            console.log(`🔍 [SEARCH DEBUG] ==========================================`);
            
            if (currentApolloPage < apolloTotalPages) {
                console.log(`🔍 [SEARCH DEBUG] ✅ More Apollo pages available (${currentApolloPage}/${apolloTotalPages})`);
                console.log(`🔍 [SEARCH DEBUG] 🔄 Getting next Apollo page: ${currentApolloPage + 1}`);
                
                // Get next Apollo page
                const nextApolloPage = currentApolloPage + 1;
                const apolloSearchResults = await executeApolloSearch(workspaceId, filters, leadType, excludeMyLeads, nextApolloPage);
                
                // Process and store new leads
                const newLeads = await processAndStoreLeads({
                    workspaceId, 
                    apolloResults: apolloSearchResults.people || apolloSearchResults.organizations || [], 
                    searchHash, 
                    userId, 
                    leadType
                });
                
                // Update existing search with new results
                const updatedResultIds = [...(existingSearch.resultIds || []), ...newLeads.map(lead => lead.id)];
                console.log(`🔍 [SEARCH DEBUG] 📊 UPDATING SEARCH WITH NEW RESULTS:`);
                console.log(`🔍 [SEARCH DEBUG] - Previous Total: ${existingSearch.resultIds?.length || 0} leads`);
                console.log(`🔍 [SEARCH DEBUG] - New Results: ${newLeads.length} leads`);
                console.log(`🔍 [SEARCH DEBUG] - Updated Total: ${updatedResultIds.length} leads`);
                console.log(`🔍 [SEARCH DEBUG] - Apollo Page Used: ${nextApolloPage}`);
                console.log(`🔍 [SEARCH DEBUG] ==========================================`);
                
                await leadSearchService.updateSearchResults(existingSearch.id, {
                    resultIds: updatedResultIds,
                    totalCount: Math.max(existingSearch.totalCount, apolloSearchResults.totalCount || 0),
                    numberLoaded: updatedResultIds.length,
                    pageLoaded: pagination.page,
                    metadata: {
                        ...existingSearch.metadata,
                        apolloRequestId: apolloSearchResults.requestId,
                        apolloCreditsUsed: (existingSearch.metadata?.apolloCreditsUsed || 0) + (apolloSearchResults.creditsUsed || 0),
                        processingTimeMs: (existingSearch.metadata?.processingTimeMs || 0) + (apolloSearchResults.processingTime || 0),
                        apolloPage: nextApolloPage,
                        apolloTotalPages: apolloTotalPages
                    }
                });
                
                // Update the apolloPage field separately
                await leadSearchService.update({ id: existingSearch.id }, { apolloPage: nextApolloPage });
                
                // Now return the requested page
                const startIndex = (requestedPage - 1) * maxResultsPerPage;
                const endIndex = startIndex + maxResultsPerPage;
                const pageResultIds = updatedResultIds.slice(startIndex, endIndex);
                
                const leadService = new LeadService();
                const leads = await leadService.findByWorkspaceAndIds(workspaceId, pageResultIds);
                const processedLeads = await processLeadsForResponse(leads, excludeMyLeads, userId, workspaceId);
                
                console.log(`🔍 [SEARCH DEBUG] 🔄 Returning results from Apollo page ${nextApolloPage} for requested page ${requestedPage}`);
                console.log(`🔍 [SEARCH DEBUG] 📊 FINAL RESULT:`);
                console.log(`🔍 [SEARCH DEBUG] - Apollo Page Used: ${nextApolloPage}`);
                console.log(`🔍 [SEARCH DEBUG] - Frontend Page Served: ${requestedPage}`);
                console.log(`🔍 [SEARCH DEBUG] - Leads Returned: ${processedLeads.length}`);
                console.log(`🔍 [SEARCH DEBUG] - Total Cached: ${updatedResultIds.length}`);
                console.log(`🔍 [SEARCH DEBUG] ==========================================`);
                
                // Calculate total pages available for frontend pagination
                const totalPagesAvailable = Math.ceil(updatedResultIds.length / maxResultsPerPage);
                console.log(`🔍 [SEARCH DEBUG] 📊 PAGINATION CALCULATION:`);
                console.log(`🔍 [SEARCH DEBUG] - Total Result IDs: ${updatedResultIds.length}`);
                console.log(`🔍 [SEARCH DEBUG] - Max Results Per Page: ${maxResultsPerPage}`);
                console.log(`🔍 [SEARCH DEBUG] - Total Pages Available: ${totalPagesAvailable}`);
                console.log(`🔍 [SEARCH DEBUG] ==========================================`);
                
                return {
                    leads: processedLeads,
                    totalCount: Math.max(existingSearch.totalCount, apolloSearchResults.totalCount || 0),
                    hasNextPage: requestedPage < totalPagesAvailable,
                    searchId: existingSearch.id,
                    filters: existingSearch.filters,
                    metadata: {
                        apolloRequestId: apolloSearchResults.requestId,
                        apolloCreditsUsed: apolloSearchResults.creditsUsed || 0,
                        processingTimeMs: apolloSearchResults.processingTime || 0,
                        resultQuality: 'high',
                        dataFreshness: new Date(),
                        apolloPage: nextApolloPage,
                        apolloTotalPages: apolloTotalPages,
                        totalPagesAvailable: totalPagesAvailable
                    }
                };
            } else {
                console.log(`🔍 [SEARCH DEBUG] ❌ No more Apollo pages available`);
                console.log(`🔍 [SEARCH DEBUG] 🔄 Returning last available page with warning`);
                
                // Return the last available page
                const lastPage = totalPagesAvailable;
                const startIndex = (lastPage - 1) * maxResultsPerPage;
                const endIndex = startIndex + maxResultsPerPage;
                const pageResultIds = existingSearch.resultIds?.slice(startIndex, endIndex) || [];
                
                const leadService = new LeadService();
                const leads = await leadService.findByWorkspaceAndIds(workspaceId, pageResultIds);
                const processedLeads = await processLeadsForResponse(leads, excludeMyLeads, userId, workspaceId);
                
                console.log(`🔍 [SEARCH DEBUG] 📊 PAGINATION CALCULATION (Last Page):`);
                console.log(`🔍 [SEARCH DEBUG] - Total Pages Available: ${totalPagesAvailable}`);
                console.log(`🔍 [SEARCH DEBUG] ==========================================`);
                
                return {
                    leads: processedLeads,
                    totalCount: existingSearch.totalCount,
                    hasNextPage: false, // No more pages available
                    searchId: existingSearch.id,
                    filters: existingSearch.filters,
                    metadata: {
                        ...existingSearch.metadata,
                        resultQuality: 'high',
                        dataFreshness: existingSearch.lastExecutedAt || existingSearch.createdAt,
                        totalPagesAvailable: totalPagesAvailable
                    }
                };
            }
        }
    }

    console.log(`🔍 [SEARCH DEBUG] ❌ No existing search found - calling Apollo API`);
    console.log(`🔍 [SEARCH DEBUG] Apollo search parameters:`, {
        workspaceId,
        filters: JSON.stringify(filters),
        leadType,
        excludeMyLeads
    });

    // Get the next Apollo page for this search criteria
    let apolloPage = 1;
    if (existingSearch && existingSearch.apolloPage) {
        apolloPage = existingSearch.apolloPage + 1; // Increment to next page
        console.log(`🔍 [SEARCH DEBUG] 🔄 INCREMENTING APOLLO PAGE: ${existingSearch.apolloPage} → ${apolloPage}`);
    } else {
        console.log(`🔍 [SEARCH DEBUG] 🆕 FIRST APOLLO PAGE: ${apolloPage}`);
    }
    console.log(`🔍 [SEARCH DEBUG] 🚀 CALLING APOLLO WITH PAGE: ${apolloPage}`);
    const apolloSearchResults = await executeApolloSearch(workspaceId, filters, leadType, excludeMyLeads, apolloPage);
    console.log(`🔍 [SEARCH DEBUG] Apollo returned:`, {
        peopleCount: apolloSearchResults.people?.length || 0,
        organizationsCount: apolloSearchResults.organizations?.length || 0,
        totalCount: apolloSearchResults.totalCount,
        requestId: apolloSearchResults.requestId,
        creditsUsed: apolloSearchResults.creditsUsed
    });
    
    // Log sample names from Apollo results to debug data mixing
    if (apolloSearchResults.people && apolloSearchResults.people.length > 0) {
        const sampleApolloNames = apolloSearchResults.people.slice(0, 3).map(p => p.name || p.firstName + ' ' + p.lastName).join(', ');
        console.log(`🔍 [SEARCH DEBUG] Sample Apollo people names: ${sampleApolloNames}`);
    }
    
    const leads = await processAndStoreLeads({workspaceId, apolloResults: apolloSearchResults.people || apolloSearchResults.organizations || [], searchHash, userId, leadType});
    console.log(`🔍 [SEARCH DEBUG] Processed and stored ${leads.length} leads in database`);
    
    // Create or update search record
    if (existingSearch) {
        console.log(`🔍 [SEARCH DEBUG] Updating existing search record: ${existingSearch.id}`);
        
        // Accumulate new results with existing results
        const updatedResultIds = [...(existingSearch.resultIds || []), ...leads.map(lead => lead.id)];
        console.log(`🔍 [SEARCH DEBUG] 📊 ACCUMULATING RESULTS: ${existingSearch.resultIds?.length || 0} existing + ${leads.length} new = ${updatedResultIds.length} total`);
        
        // Update search results
        await leadSearchService.updateSearchResults(existingSearch.id, {
            resultIds: updatedResultIds,
            totalCount: apolloSearchResults.totalCount || updatedResultIds.length,
            numberLoaded: updatedResultIds.length,
            pageLoaded: pagination.page,
            metadata: {
                apolloRequestId: apolloSearchResults.requestId,
                apolloCreditsUsed: apolloSearchResults.creditsUsed,
                processingTimeMs: apolloSearchResults.processingTime,
                resultQuality: 'high',
                dataFreshness: new Date(),
                apolloPage: apolloPage, // Track which Apollo page we're on (incremented)
                apolloTotalPages: Math.ceil((apolloSearchResults.totalCount || 0) / 100) // Calculate total Apollo pages
            }
        });
        
        // Update the apolloPage field separately
        await leadSearchService.update({ id: existingSearch.id }, { apolloPage });
    } else {
        console.log(`🔍 [SEARCH DEBUG] Creating new search record with hash: ${searchHash}`);
        
        // Clean filters before storing - excludeMyLeads is NOT a search parameter
        const cleanFilters = { ...filters };
        delete cleanFilters.excludeMyLeads;
        
        console.log(`🔍 [SEARCH DEBUG] 🔧 FILTERS CLEANING:`);
        console.log(`🔍 [SEARCH DEBUG] - Original filters: ${JSON.stringify(filters, null, 2)}`);
        console.log(`🔍 [SEARCH DEBUG] - Cleaned filters: ${JSON.stringify(cleanFilters, null, 2)}`);
        console.log(`🔍 [SEARCH DEBUG] - excludeMyLeads removed from search criteria`);
        console.log(`🔍 [SEARCH DEBUG] ==========================================`);
        
        await leadSearchService.createSearch({
            workspaceId,
            searchHash,
            filters: cleanFilters,  // Store clean filters
            pagination,
            resultIds: leads.map(lead => lead.id),
            totalCount: apolloSearchResults.totalCount || leads.length,
            numberLoaded: leads.length,
            pageLoaded: pagination.page,
            createdById: userId,
            isActive: true,
            apolloPage: apolloPage, // Pass apolloPage to the service
            metadata: {
                apolloRequestId: apolloSearchResults.requestId,
                apolloCreditsUsed: apolloSearchResults.creditsUsed,
                processingTimeMs: apolloSearchResults.processingTime,
                resultQuality: 'high',
                dataFreshness: new Date(),
                apolloPage: apolloPage, // Track which Apollo page we're on
                apolloTotalPages: Math.ceil((apolloSearchResults.totalCount || 0) / 100), // Calculate total Apollo pages
                totalPagesAvailable: Math.ceil(leads.length / 50) // Add total pages available for this search
            }
        });
        
        console.log(`🔍 [SEARCH DEBUG] ✅ SEARCH RECORD CREATED:`);
        console.log(`🔍 [SEARCH DEBUG] - Search Hash: ${searchHash}`);
        console.log(`🔍 [SEARCH DEBUG] - Cleaned Filters: ${JSON.stringify(cleanFilters, null, 2)}`);
        console.log(`🔍 [SEARCH DEBUG] - excludeMyLeads: NOT stored in search record`);
        console.log(`🔍 [SEARCH DEBUG] - This search can now be cached for future requests`);
        console.log(`🔍 [SEARCH DEBUG] ==========================================`);
    }

    const searchRecord = existingSearch || await leadSearchService.findByWorkspaceAndHash(workspaceId, searchHash);

    // Apply data privacy and exclude my leads
    const processedLeads = await processLeadsForResponse(leads, excludeMyLeads, userId, workspaceId);
    console.log(`🔍 [SEARCH DEBUG] After privacy processing: ${processedLeads.length} leads`);
    
    // Get the total accumulated results for proper pagination
    const totalAccumulatedResults = existingSearch ? 
        (existingSearch.resultIds?.length || 0) + leads.length : 
        leads.length;
    
    // Apply pagination to respect user's requested limit (max 50 per page)
    const maxLimit = Math.min(pagination.limit, 50); // Force max 50 results per page
    const startIndex = (pagination.page - 1) * maxLimit;
    const endIndex = startIndex + maxLimit;
    const paginatedLeads = processedLeads.slice(startIndex, endIndex);
    
    console.log(`🔍 [SEARCH DEBUG] Final pagination:`, {
        maxLimit,
        startIndex,
        endIndex,
        paginatedLeadsCount: paginatedLeads.length
    });

    console.log(`🔍 [SEARCH DEBUG] 🆕 Returning FRESH results from Apollo API`);
    console.log(`🔍 [SEARCH DEBUG] 📊 SEARCH SUMMARY:`);
    console.log(`🔍 [SEARCH DEBUG] - Decision: APOLLO_CALLED`);
    console.log(`🔍 [SEARCH DEBUG] - Reason: No existing search found`);
    console.log(`🔍 [SEARCH DEBUG] - Search Hash: ${searchHash}`);
    console.log(`🔍 [SEARCH DEBUG] - Apollo Page Used: ${apolloPage}`);
    console.log(`🔍 [SEARCH DEBUG] - Apollo Results: ${apolloSearchResults.people?.length || apolloSearchResults.organizations?.length || 0}`);
    console.log(`🔍 [SEARCH DEBUG] - Final Results: ${paginatedLeads.length}`);
    console.log(`🔍 [SEARCH DEBUG] - Total Accumulated: ${totalAccumulatedResults}`);
    console.log(`🔍 [SEARCH DEBUG] - Total Available from Apollo: ${apolloSearchResults.totalCount || leads.length}`);
    console.log(`🔍 [SEARCH DEBUG] - excludeMyLeads Filtering: Applied to Apollo results`);
    console.log(`🔍 [SEARCH DEBUG] - Search Record: Created for future caching`);
    console.log(`🔍 [SEARCH DEBUG] ==========================================`);
    
    // Calculate total pages available for frontend pagination
    const totalPagesAvailable = Math.ceil(totalAccumulatedResults / 50);
    console.log(`🔍 [SEARCH DEBUG] 📊 PAGINATION CALCULATION (New Search):`);
    console.log(`🔍 [SEARCH DEBUG] - Total Accumulated Results: ${totalAccumulatedResults}`);
    console.log(`🔍 [SEARCH DEBUG] - Total Pages Available: ${totalPagesAvailable}`);
    console.log(`🔍 [SEARCH DEBUG] ==========================================`);
    
    return {
        leads: paginatedLeads,
        totalCount: apolloSearchResults.totalCount || totalAccumulatedResults,
        hasNextPage: totalAccumulatedResults > endIndex,
        searchId: searchRecord?.id || '',
        filters,
        metadata: {
            ...searchRecord?.metadata,
            totalPagesAvailable: totalPagesAvailable
        }
    };
}


async function executeApolloSearch(workspaceId: string, filters: SearchFilters, leadType: LeadType = LeadType.Person, excludeMyLeads: boolean = false, apolloPage: number = 1) {
    console.log(`🔍 [APOLLO DEBUG] 🚀 STARTING APOLLO SEARCH EXECUTION`);
    console.log(`🔍 [APOLLO DEBUG] ==========================================`);
    console.log(`🔍 [APOLLO DEBUG] 📋 SEARCH PARAMETERS:`);
    console.log(`🔍 [APOLLO DEBUG] - Workspace ID: ${workspaceId}`);
    console.log(`🔍 [APOLLO DEBUG] - Lead Type: ${leadType}`);
    console.log(`🔍 [APOLLO DEBUG] - Exclude My Leads: ${excludeMyLeads}`);
    console.log(`🔍 [APOLLO DEBUG] - Apollo Page: ${apolloPage}`);
    console.log(`🔍 [APOLLO DEBUG] - Filters: ${JSON.stringify(filters, null, 2)}`);
    console.log(`🔍 [APOLLO DEBUG] ==========================================`);
    
    // Convert your filters to Apollo format
    const apolloFilters = convertFiltersToApolloFormat(filters, leadType);
    console.log(`🔍 [APOLLO DEBUG] 🔧 FILTER CONVERSION:`);
    console.log(`🔍 [APOLLO DEBUG] - Original Filters: ${JSON.stringify(filters, null, 2)}`);
    console.log(`🔍 [APOLLO DEBUG] - Apollo Format: ${JSON.stringify(apolloFilters, null, 2)}`);
    console.log(`🔍 [APOLLO DEBUG] ==========================================`);
    
    let result;
    
    try {
        if (leadType === LeadType.Person) {
            console.log(`🔍 [APOLLO DEBUG] 🚀 CALLING APOLLO INTEGRATION: advancedPeopleSearch`);
            console.log(`🔍 [APOLLO DEBUG] ==========================================`);
            console.log(`🔍 [APOLLO DEBUG] 📡 INTEGRATION CALL DETAILS:`);
            console.log(`🔍 [APOLLO DEBUG] - Integration: apollo`);
            console.log(`🔍 [APOLLO DEBUG] - Action: advancedPeopleSearch`);
            console.log(`🔍 [APOLLO DEBUG] - Apollo Page: ${apolloPage}`);
            console.log(`🔍 [APOLLO DEBUG] - Search Parameters: ${JSON.stringify(apolloFilters)}`);
            console.log(`🔍 [APOLLO DEBUG] - Exclude My Leads: ${excludeMyLeads}`);
            console.log(`🔍 [APOLLO DEBUG] ==========================================`);
            
            result = await ExecuteWorkspaceIntegrationActionNoAuth(
                workspaceId,
                'apollo',
                {
                    name: 'advancedPeopleSearch',
                    type: 'action',
                    mode: 'run',
                    propsValue: {
                        searchParams: JSON.stringify(apolloFilters),
                        excludeMyLeads: excludeMyLeads.toString(),
                        page: apolloPage.toString()
                    }
                },
                'apollo-default'
            );
            
            console.log(`🔍 [APOLLO DEBUG] ✅ Apollo integration call completed successfully`);
            console.log(`🔍 [APOLLO DEBUG] 📥 Raw Response Received:`, JSON.stringify(result, null, 2));
        } else {
            console.log(`🔍 [APOLLO DEBUG] 🚀 CALLING APOLLO INTEGRATION: searchOrganizations`);
            console.log(`🔍 [APOLLO DEBUG] ==========================================`);
            console.log(`🔍 [APOLLO DEBUG] 📡 INTEGRATION CALL DETAILS:`);
            console.log(`🔍 [APOLLO DEBUG] - Integration: apollo`);
            console.log(`🔍 [APOLLO DEBUG] - Action: searchOrganizations`);
            console.log(`🔍 [APOLLO DEBUG] - Apollo Page: ${apolloPage}`);
            console.log(`🔍 [APOLLO DEBUG] - Search Parameters: ${JSON.stringify(apolloFilters)}`);
            console.log(`🔍 [APOLLO DEBUG] - Exclude My Leads: ${excludeMyLeads}`);
            console.log(`🔍 [APOLLO DEBUG] ==========================================`);
            
            result = await ExecuteWorkspaceIntegrationActionNoAuth(
                workspaceId,
                'apollo',
                {
                    name: 'searchOrganizations',
                    type: 'action',
                    mode: 'run',
                    propsValue: {
                        searchParams: JSON.stringify(apolloFilters),
                        excludeMyLeads: excludeMyLeads.toString(),
                        page: apolloPage.toString()
                    }
                },
                'apollo-default'
            );
            
            console.log(`🔍 [APOLLO DEBUG] ✅ Apollo integration call completed successfully`);
            console.log(`🔍 [APOLLO DEBUG] 📥 Raw Response Received:`, JSON.stringify(result, null, 2));
        }
        
        console.log(`🔍 [APOLLO DEBUG] Apollo integration result:`, {
            hasResult: !!result,
            resultKeys: result ? Object.keys(result) : [],
            rawResult: JSON.stringify(result, null, 2)
        });
        
    } catch (error) {
        console.error(`🔍 [APOLLO DEBUG] ERROR calling Apollo API:`, error);
        throw error;
    }

    // Process the final result
    console.log(`🔍 [APOLLO DEBUG] 🔍 PROCESSING FINAL RESULT:`);
    console.log(`🔍 [APOLLO DEBUG] ==========================================`);
    
    // Handle both nested and direct response structures
    const finalResult = result?.result || result || { people: [], organizations: [], totalCount: 0 };
    
    console.log(`🔍 [APOLLO DEBUG] 📊 FINAL PROCESSED RESULT:`);
    console.log(`🔍 [APOLLO DEBUG] - People Count: ${finalResult.people?.length || 0}`);
    console.log(`🔍 [APOLLO DEBUG] - Organizations Count: ${finalResult.organizations?.length || 0}`);
    console.log(`🔍 [APOLLO DEBUG] - Total Count: ${finalResult.totalCount}`);
    console.log(`🔍 [APOLLO DEBUG] - Apollo Page Requested: ${apolloPage}`);
    console.log(`🔍 [APOLLO DEBUG] - Request ID: ${finalResult.requestId || finalResult.request_id || 'N/A'}`);
    console.log(`🔍 [APOLLO DEBUG] - Credits Used: ${finalResult.creditsUsed || finalResult.credits_used || 'N/A'}`);
    
    // Log sample results for verification
    if (finalResult.people && finalResult.people.length > 0) {
        const samplePeople = finalResult.people.slice(0, 3).map(p => ({ name: p.name, company: p.company }));
        console.log(`🔍 [APOLLO DEBUG] 👥 SAMPLE PEOPLE RESULTS:`);
        console.log(`🔍 [APOLLO DEBUG] - Sample 1: ${JSON.stringify(samplePeople[0])}`);
        console.log(`🔍 [APOLLO DEBUG] - Sample 2: ${JSON.stringify(samplePeople[1])}`);
        console.log(`🔍 [APOLLO DEBUG] - Sample 3: ${JSON.stringify(samplePeople[2])}`);
    }
    
    if (finalResult.organizations && finalResult.organizations.length > 0) {
        const sampleOrgs = finalResult.organizations.slice(0, 3).map(o => ({ name: o.name, industry: o.industry }));
        console.log(`🔍 [APOLLO DEBUG] 🏢 SAMPLE ORGANIZATION RESULTS:`);
        console.log(`🔍 [APOLLO DEBUG] - Sample 1: ${JSON.stringify(sampleOrgs[0])}`);
        console.log(`🔍 [APOLLO DEBUG] - Sample 2: ${JSON.stringify(sampleOrgs[1])}`);
        console.log(`🔍 [APOLLO DEBUG] - Sample 3: ${JSON.stringify(sampleOrgs[2])}`);
    }
    
    console.log(`🔍 [APOLLO DEBUG] ==========================================`);
    console.log(`🔍 [APOLLO DEBUG] ✅ APOLLO SEARCH EXECUTION COMPLETED SUCCESSFULLY`);
    console.log(`🔍 [APOLLO DEBUG] 🚀 RETURNING RESULT:`);
    console.log(`🔍 [APOLLO DEBUG] - Total People: ${finalResult.people?.length || 0}`);
    console.log(`🔍 [APOLLO DEBUG] - Total Organizations: ${finalResult.organizations?.length || 0}`);
    console.log(`🔍 [APOLLO DEBUG] - Apollo Page: ${apolloPage}`);
    console.log(`🔍 [APOLLO DEBUG] ==========================================`);
    
    // Return the processed result with proper structure
    return {
        people: finalResult.people || [],
        organizations: finalResult.organizations || [],
        totalCount: finalResult.totalCount || 0,
        requestId: finalResult.requestId || finalResult.request_id || 'N/A',
        creditsUsed: finalResult.creditsUsed || finalResult.credits_used || 0,
        processingTime: finalResult.processingTime || 0
    };
}



// Convert your filter format to Apollo's expected format
function convertFiltersToApolloFormat(filters: SearchFilters, leadType: LeadType): Record<string, any> {
    const apolloFilters: Record<string, any> = {};
    
    if (leadType === LeadType.Person && filters.person) {
        const person = filters.person;
        
        // Job titles
        if (person.jobTitles && person.jobTitles.length > 0) {
            apolloFilters.person_titles = person.jobTitles;
        }
        
        // Management level
        if (person.managementLevel && person.managementLevel.length > 0) {
            apolloFilters.person_management_levels = person.managementLevel;
        }
        
        // Job function
        if (person.jobFunction && person.jobFunction.length > 0) {
            apolloFilters.person_job_functions = person.jobFunction;
        }
        
        // Location
        if (person.location && person.location.length > 0) {
            apolloFilters.person_locations = person.location;
        }
        
        // Seniority
        if (person.seniority && person.seniority.length > 0) {
            apolloFilters.person_seniority_levels = person.seniority;
        }
        
        // Departments
        if (person.departments && person.departments.length > 0) {
            apolloFilters.person_departments = person.departments;
        }
        
        // Skills
        if (person.skills && person.skills.length > 0) {
            apolloFilters.person_skills = person.skills;
        }
    }
    
    if (leadType === LeadType.Company && filters.company) {
        const company = filters.company;
        
        // Industry - Handle both field names for compatibility
        if ((company.industry && company.industry.length > 0) || 
            (company.industries && company.industries.length > 0)) {
            apolloFilters.organization_industries = company.industry || company.industries;
        }
        
        // Company size - Apollo expects organization_num_employees_ranges
        if (company.companySize && company.companySize.length > 0) {
            apolloFilters.organization_num_employees_ranges = company.companySize;
        }
        
        // Company type
        if (company.companyType && company.companyType.length > 0) {
            apolloFilters.organization_types = company.companyType;
        }
        
        // Location
        if (company.location && company.location.length > 0) {
            apolloFilters.organization_locations = company.location;
        }
        
        // Revenue (handle min/max structure) - Apollo expects revenue_range[min] and revenue_range[max]
        if (company.revenue) {
            if (company.revenue.min !== undefined || company.revenue.max !== undefined) {
                if (company.revenue.min !== undefined) {
                    apolloFilters['revenue_range[min]'] = company.revenue.min;
                }
                if (company.revenue.max !== undefined) {
                    apolloFilters['revenue_range[max]'] = company.revenue.max;
                }
            }
        }
        
        // Employees (handle min/max structure) - Apollo expects organization_num_employees_ranges
        if (company.employees) {
            if (company.employees.min !== undefined || company.employees.max !== undefined) {
                apolloFilters.organization_num_employees_ranges = [];
                if (company.employees.min !== undefined) {
                    apolloFilters.organization_num_employees_ranges.push(`${company.employees.min}+`);
                }
                if (company.employees.max !== undefined) {
                    apolloFilters.organization_num_employees_ranges.push(`0-${company.employees.max}`);
                }
            }
        }
        
        // Founded year (handle min/max structure) - Apollo expects founded_year_range[min] and founded_year_range[max]
        if (company.foundedYear) {
            if (company.foundedYear.min !== undefined || company.foundedYear.max !== undefined) {
                if (company.foundedYear.min !== undefined) {
                    apolloFilters['founded_year_range[min]'] = company.foundedYear.min;
                }
                if (company.foundedYear.max !== undefined) {
                    apolloFilters['founded_year_range[max]'] = company.foundedYear.max;
                }
            }
        }
        
        // Technologies - Apollo expects currently_using_any_of_technology_uids
        if (company.technologies && company.technologies.length > 0) {
            apolloFilters.currently_using_any_of_technology_uids = company.technologies;
        }
        
        // Keywords
        if (company.keywords && company.keywords.length > 0) {
            apolloFilters.q_organization_keyword_tags = company.keywords;
        }
    }
    
    // Add debugging for Apollo filters
    console.log(`🔍 [APOLLO DEBUG] 🔧 FILTER CONVERSION:`);
    console.log(`🔍 [APOLLO DEBUG] - Original filters: ${JSON.stringify(filters, null, 2)}`);
    console.log(`🔍 [APOLLO DEBUG] - Lead type: ${leadType}`);
    console.log(`🔍 [APOLLO DEBUG] - Converted Apollo filters: ${JSON.stringify(apolloFilters, null, 2)}`);
    console.log(`🔍 [APOLLO DEBUG] ==========================================`);
    
    // Search queries are no longer handled here - only sidebar filters are used
    // The search input field serves a different purpose in the UI
    
    return apolloFilters;
}

// Search query conversion removed - only sidebar filters are used for Apollo searches

interface ProcessAndStoreLeadsData {
    workspaceId: string;
    apolloResults: ApolloPersonData[] | ApolloCompanyData[];
    searchHash: string;
    userId: string;
    leadType: LeadType;
}


async function processAndStoreLeads( data: ProcessAndStoreLeadsData): Promise<Lead[]> {
    console.log(`🔍 [STORAGE DEBUG] 🚀 STARTING LEAD STORAGE PROCESS`);
    console.log(`🔍 [STORAGE DEBUG] ==========================================`);
    console.log(`🔍 [STORAGE DEBUG] 📋 INPUT DATA:`);
    console.log(`🔍 [STORAGE DEBUG] - Workspace ID: ${data.workspaceId}`);
    console.log(`🔍 [STORAGE DEBUG] - Search Hash: ${data.searchHash}`);
    console.log(`🔍 [STORAGE DEBUG] - User ID: ${data.userId}`);
    console.log(`🔍 [STORAGE DEBUG] - Lead Type: ${data.leadType}`);
    console.log(`🔍 [STORAGE DEBUG] - Apollo Results Count: ${data.apolloResults.length}`);
    console.log(`🔍 [STORAGE DEBUG] ==========================================`);
    
    validateWorkspaceId(data.workspaceId);
    if (!data.searchHash || typeof data.searchHash !== 'string' || data.searchHash.trim().length === 0) {
        throw new RequiredParameterError('Search hash is required');
    }
    if (!data.userId || typeof data.userId !== 'string' || data.userId.trim().length === 0) {
        throw new RequiredParameterError('User ID is required');
    }
    
    const leadService = new LeadService();
    const processedLeads: Lead[] = [];
    let newLeadsCount = 0;
    let existingLeadsCount = 0;

    console.log(`🔍 [STORAGE DEBUG] 🔄 PROCESSING ${data.apolloResults.length} APOLLO RESULTS`);
    
    // Log sample Apollo results to see what we're getting
    if (data.apolloResults.length > 0) {
        const sampleApollo = data.apolloResults.slice(0, 3);
        console.log(`🔍 [STORAGE DEBUG] 👥 SAMPLE APOLLO RESULTS:`);
        console.log(`🔍 [STORAGE DEBUG] - Sample 1: ${JSON.stringify(sampleApollo[0])}`);
        console.log(`🔍 [STORAGE DEBUG] - Sample 2: ${JSON.stringify(sampleApollo[1])}`);
        console.log(`🔍 [STORAGE DEBUG] - Sample 3: ${JSON.stringify(sampleApollo[2])}`);
        console.log(`🔍 [STORAGE DEBUG] ==========================================`);
        
        // Add detailed field analysis for the first result
        if (sampleApollo[0]) {
            console.log(`🔍 [STORAGE DEBUG] 🔍 DETAILED FIELD ANALYSIS (Sample 1):`);
            console.log(`🔍 [STORAGE DEBUG] - Has ID: ${!!sampleApollo[0].id}`);
            console.log(`🔍 [STORAGE DEBUG] - ID value: ${sampleApollo[0].id}`);
            console.log(`🔍 [STORAGE DEBUG] - Has name: ${!!sampleApollo[0].name}`);
            console.log(`🔍 [STORAGE DEBUG] - Name value: ${sampleApollo[0].name}`);
            console.log(`🔍 [STORAGE DEBUG] - All keys: ${Object.keys(sampleApollo[0]).join(', ')}`);
            console.log(`🔍 [STORAGE DEBUG] ==========================================`);
        }
    }

    for (const apolloData of data.apolloResults) {
        console.log(`🔍 [STORAGE DEBUG] 🔄 PROCESSING APOLLO DATA:`, {
            hasId: !!apolloData.id,
            id: apolloData.id,
            hasName: !!apolloData.name,
            name: apolloData.name,
            leadType: data.leadType,
            allKeys: Object.keys(apolloData)
        });
        
        if (!apolloData.id) {
            console.log(`🔍 [STORAGE DEBUG] 🚫 Skipping result without ID`);
            continue;
        }

        try {
            validateApolloData(apolloData, data.leadType);
            console.log(`🔍 [STORAGE DEBUG] ✅ Validation passed for Apollo data`);
        } catch (validationError: any) { // Cast to any to access .message
            console.log(`🔍 [STORAGE DEBUG] ❌ Validation failed for Apollo data:`, validationError.message);
            console.log(`🔍 [STORAGE DEBUG] ❌ Validation error details:`, {
                error: validationError.message,
                stack: validationError.stack,
                apolloData: {
                    id: apolloData.id,
                    name: apolloData.name,
                    type: data.leadType
                }
            });
            continue;
        }

        let existingLead = await leadService.findByWorkspaceAndApolloId(data.workspaceId, apolloData.id);

        if (existingLead) {
            console.log(`🔍 [STORAGE DEBUG] Found existing lead: ${existingLead.name} (ID: ${existingLead.id})`);
            console.log(`🔍 [STORAGE DEBUG] Existing lead details:`, {
                name: existingLead.name,
                apolloId: existingLead.apolloId,
                searchHashes: existingLead.searchHashes,
                createdAt: existingLead.createdAt
            });
            // Update search hashes
            const searchHashes = existingLead.searchHashes || [];
            if (!searchHashes.includes(data.searchHash)) {
                console.log(`🔍 [STORAGE DEBUG] Adding new search hash to existing lead`);
                searchHashes.push(data.searchHash);
                await leadService.updateSearchHashes(existingLead.id, searchHashes);
            } else {
                console.log(`🔍 [STORAGE DEBUG] Search hash already exists for this lead`);
            }
            processedLeads.push(existingLead);
            existingLeadsCount++;
        } else {
            console.log(`🔍 [STORAGE DEBUG] Creating new lead for Apollo ID: ${apolloData.id}`);
            let normalizedData: NormalizedLeadData;
            try {
                if (data.leadType === LeadType.Person) {
                    normalizedData = normalizeApolloPersonData(apolloData as ApolloPersonData);
                } else {
                    normalizedData = normalizeApolloCompanyData(apolloData as ApolloCompanyData);
                }
                console.log(`🔍 [STORAGE DEBUG] ✅ Normalization completed:`, {
                    name: normalizedData.name,
                    company: normalizedData.company,
                    companyDomain: normalizedData.companyDomain
                });
            } catch (normalizeError: any) {
                console.log(`🔍 [STORAGE DEBUG] ❌ Normalization failed:`, normalizeError.message);
                console.log(`🔍 [STORAGE DEBUG] ❌ Normalization error details:`, {
                    error: normalizeError.message,
                    stack: normalizeError.stack,
                    apolloData: {
                        id: apolloData.id,
                        name: apolloData.name,
                        type: data.leadType
                    }
                });
                continue;
            }

            try {
                validateNormalizedLeadData(normalizedData);
                console.log(`🔍 [STORAGE DEBUG] ✅ Normalized data validation passed`);
            } catch (validationError: any) {
                console.log(`🔍 [STORAGE DEBUG] ❌ Normalized data validation failed:`, validationError.message);
                console.log(`🔍 [STORAGE DEBUG] ❌ Normalized data validation error details:`, {
                    error: validationError.message,
                    stack: validationError.stack,
                    normalizedData: {
                        name: normalizedData.name,
                        company: normalizedData.company,
                        companyDomain: normalizedData.companyDomain
                    }
                });
                continue;
            }

            try {
                const newLead = await leadService.createLead({
                    workspaceId: data.workspaceId,
                    apolloId: apolloData.id,
                    type: data.leadType,
                    source: LeadSource.Apollo,
                    apolloData,
                    normalizedData,
                    searchHashes: [data.searchHash],
                    email: normalizedData.email,
                    name: normalizedData.name,
                    companyDomain: normalizedData.companyDomain,
                    createdById: data.userId
                });
                
                console.log(`🔍 [STORAGE DEBUG] ✅ Created new lead: ${newLead.name} (ID: ${newLead.id})`);
                processedLeads.push(newLead);
                newLeadsCount++;
            } catch (createError: any) {
                console.log(`🔍 [STORAGE DEBUG] ❌ Lead creation failed:`, createError.message);
                console.log(`🔍 [STORAGE DEBUG] ❌ Lead creation error details:`, {
                    error: createError.message,
                    stack: createError.stack,
                    apolloData: {
                        id: apolloData.id,
                        name: apolloData.name,
                        type: data.leadType
                    },
                    normalizedData: {
                        name: normalizedData.name,
                        company: normalizedData.company,
                        companyDomain: normalizedData.companyDomain
                    }
                });
                continue;
            }
        }
    }

    console.log(`🔍 [STORAGE DEBUG] ==========================================`);
    console.log(`🔍 [STORAGE DEBUG] 📊 STORAGE SUMMARY:`);
    console.log(`🔍 [STORAGE DEBUG] - Total Processed: ${processedLeads.length} leads`);
    console.log(`🔍 [STORAGE DEBUG] - New Leads Created: ${newLeadsCount} leads`);
    console.log(`🔍 [STORAGE DEBUG] - Existing Leads Updated: ${existingLeadsCount} leads`);
    console.log(`🔍 [STORAGE DEBUG] - Search Hash: ${data.searchHash}`);
    console.log(`🔍 [STORAGE DEBUG] - Workspace ID: ${data.workspaceId}`);
    console.log(`🔍 [STORAGE DEBUG] ==========================================`);
    
    console.log(`🔍 [STORAGE DEBUG] ✅ LEAD STORAGE PROCESS COMPLETED SUCCESSFULLY`);
    console.log(`🔍 [STORAGE DEBUG] 🚀 RETURNING ${processedLeads.length} PROCESSED LEADS`);
    console.log(`🔍 [STORAGE DEBUG] ==========================================`);

    return processedLeads;
}

async function processLeadsForResponse(leads: Lead[], excludeMyLeads: boolean, userId: string,  workspaceId: string): Promise<Lead[]> {
    console.log(`🔒 [FILTER DEBUG] ==========================================`);
    console.log(`🔒 [FILTER DEBUG] 🚀 STARTING LEAD FILTERING PROCESS`);
    console.log(`🔒 [FILTER DEBUG] ==========================================`);
    console.log(`🔒 [FILTER DEBUG] Input Parameters:`);
    console.log(`🔒 [FILTER DEBUG] - Total leads to process: ${leads.length}`);
    console.log(`🔒 [FILTER DEBUG] - excludeMyLeads: ${excludeMyLeads}`);
    console.log(`🔒 [FILTER DEBUG] - User ID: ${userId}`);
    console.log(`🔒 [FILTER DEBUG] - Workspace ID: ${workspaceId}`);
    console.log(`🔒 [FILTER DEBUG] ==========================================`);
    
    const leadUnlockService = new LeadUnlockService();
    
    if (!excludeMyLeads) {
        console.log(`🔒 [FILTER DEBUG] ✅ excludeMyLeads is FALSE - showing ALL leads`);
        console.log(`🔒 [FILTER DEBUG] - No filtering applied`);
        console.log(`🔒 [FILTER DEBUG] - All ${leads.length} leads will be returned`);
        console.log(`🔒 [FILTER DEBUG] ==========================================`);
        
        // If not excluding, return all leads with privacy protection
        return leads.map(lead => ({
            ...lead,
            normalizedData: {
                ...lead.normalizedData,
                email: lead.isUnlocked ? lead.normalizedData.email : '<EMAIL>',
                phone: lead.isUnlocked ? lead.normalizedData.phone : '<EMAIL>'
            }
        }));
    }
    
    console.log(`🔒 [FILTER DEBUG] 🚫 excludeMyLeads is TRUE - filtering out owned leads`);
    console.log(`🔒 [FILTER DEBUG] - Will exclude leads owned by user ${userId} in workspace ${workspaceId}`);
    console.log(`🔒 [FILTER DEBUG] ==========================================`);
    
    // If excluding my leads, get unlocked leads and filter them out
    const unlockedLeadIds = await leadUnlockService.getUnlockedLeadsForUser(workspaceId, userId);
    console.log(`🔒 [FILTER DEBUG] 📊 UNLOCKED LEADS LOOKUP:`);
    console.log(`🔒 [FILTER DEBUG] - Found ${unlockedLeadIds.length} leads owned by user`);
    console.log(`🔒 [FILTER DEBUG] - These leads will be EXCLUDED from results`);
    console.log(`🔒 [FILTER DEBUG] ==========================================`);
    
    const filteredLeads = leads.filter(lead => !unlockedLeadIds.includes(lead.id));
    console.log(`🔒 [FILTER DEBUG] ✅ FILTERING COMPLETED:`);
    console.log(`🔒 [FILTER DEBUG] - Before filtering: ${leads.length} leads`);
    console.log(`🔒 [FILTER DEBUG] - After filtering: ${filteredLeads.length} leads`);
    console.log(`🔒 [FILTER DEBUG] - Filtered out: ${leads.length - filteredLeads.length} leads`);
    console.log(`🔒 [FILTER DEBUG] ==========================================`);
    
    return filteredLeads
        .map(lead => ({
            ...lead,
            normalizedData: {
                ...lead.normalizedData,
                email: lead.isUnlocked ? lead.normalizedData.email : '<EMAIL>',
                phone: lead.isUnlocked ? lead.normalizedData.phone : '<EMAIL>'
            }
        }));
}

export const GetMyLeads = async (
    userId: string,
    workspaceId: string,
    options: LeadFilterOptions
) => {
    return await GetMyLeadsByType(userId, workspaceId, options, LeadType.Person);
};

export const GetMyCompanyLeads = async (
    userId: string,
    workspaceId: string,
    options: LeadFilterOptions
) => {
    return await GetMyLeadsByType(userId, workspaceId, options, LeadType.Company);
};

interface GetMyLeadsByTypeData {
    userId: string;
    workspaceId: string;
    options: LeadFilterOptions;
    leadType: LeadType;
}

async function GetMyLeadsByType(   userId: string,workspaceId: string,options: LeadFilterOptions, leadType: LeadType) {
    const member = await GetMyWorkspace(userId, workspaceId);
    if (!member) {
        throw new UnauthorizedError(ErrorMessage.UnableToAuthorize);
    }

    if (!HasPermission(member, WorkspacePermission.ReadWorkspace)) {
        throw new UnauthorizedError(ErrorMessage.UnableToAuthorize);
    }

    const { page, limit, search, filters } = options;
    
    const leadService = new LeadService();
    const leadUnlockService = new LeadUnlockService();

    const unlockedLeadIds = await leadUnlockService.getUnlockedLeadsForUser(workspaceId, userId);

    if (unlockedLeadIds.length === 0) {
        return {
            leads: [],
            totalCount: 0,
            hasNextPage: false
        };
    }

    const unlockedLeads = await leadService.findByWorkspaceAndType(workspaceId, leadType);
    
    const filteredLeads = unlockedLeads.filter(lead => unlockedLeadIds.includes(lead.id));

    let finalLeads = filteredLeads;
    if (search) {
        finalLeads = filteredLeads.filter(lead => 
            lead.name?.toLowerCase().includes(search.toLowerCase()) ||
            lead.normalizedData.email?.toLowerCase().includes(search.toLowerCase()) ||
            lead.normalizedData.company?.toLowerCase().includes(search.toLowerCase())
        );
    }

    const offset = (page - 1) * limit;
    const paginatedLeads = finalLeads.slice(offset, offset + limit);

    return {
        leads: paginatedLeads,
        totalCount: finalLeads.length,
        hasNextPage: (page * limit) < finalLeads.length
    };
}

export const UnlockLead = async (userId: string,workspaceId: string,leadId: string,request: UnlockLeadRequest) => {
    validateWorkspaceId(workspaceId);
    validateLeadId(leadId);
    
    const member = await GetMyWorkspace(userId, workspaceId);
    if (!member) {
        throw new UnauthorizedError(ErrorMessage.UnableToAuthorize);
    }

    if (!HasPermission(member, WorkspacePermission.ReadWorkspace)) {
        throw new UnauthorizedError(ErrorMessage.UnableToAuthorize);
    }

    const leadService = new LeadService();
    const leadUnlockService = new LeadUnlockService();

    const lead = await leadService.findOne({ id: leadId, workspaceId });
    if (!lead) {
        throw new NotfoundError(ErrorMessage.EntityNotFound);
    }

    const existingUnlock = await leadUnlockService.findExistingUnlock(workspaceId, leadId, userId);
    if (existingUnlock) {
        return {
            lead,
            unlock: existingUnlock,
            alreadyUnlocked: true
        };
    }

    let enrichedData = null;
    if (lead.apolloId) {
        try {
            const actionName = lead.type === LeadType.Person ? 'getPerson' : 'organizationEnrichment';
            const propsValue = lead.type === LeadType.Person 
                ? { personId: lead.apolloId }
                : { domain: lead.companyDomain };

            console.log(`Attempting Apollo enrichment with action: ${actionName}`, propsValue);

            const apolloResult = await ExecuteWorkspaceIntegrationActionNoAuth(
                workspaceId,
                'apollo',
                {
                    name: actionName,
                    type: 'action',
                    mode: 'run',
                    propsValue
                },
                'apollo-default'
            );

            if (apolloResult && apolloResult.result) {
                enrichedData = apolloResult.result;
                console.log('Apollo enrichment successful:', enrichedData);
            } else {
                console.warn('Apollo enrichment returned no result');
            }
        } catch (error) {
            console.error('Failed to enrich lead from Apollo:', error);
            // Continue without enrichment - this is not a critical failure
        }
    }

    const unlock = await leadUnlockService.createUnlock({
        workspaceId,
        leadId,
        unlockedBy: userId,
        unlockType: request.unlockType,
        unlockSource: request.source,
        apolloData: enrichedData,
        enrichedData: enrichedData,
        creditsUsed: 1,
        isSuccessful: true,
        unlockedAt: new Date()
    });

    await leadService.markAsUnlocked(leadId, enrichedData);

    return {
        lead,
        unlock,
        alreadyUnlocked: false
    };
};


export const SaveSearch = async (userId: string, workspaceId: string,request: SaveSearchRequest) => {
    validateWorkspaceId(workspaceId);
    validateSearchFilters(request.filters);
    
    if (!request.name || typeof request.name !== 'string' || request.name.trim().length === 0) {
        throw new RequiredParameterError('Search name is required');
    }
    
    const member = await GetMyWorkspace(userId, workspaceId);
    if (!member) {
        throw new UnauthorizedError(ErrorMessage.UnableToAuthorize);
    }
    
    if (!HasPermission(member, WorkspacePermission.UpdateWorkspace)) {
        throw new UnauthorizedError(ErrorMessage.UnableToAuthorize);
    }
    
    const leadSearchService = new LeadSearchService();
    
    let searchRecord: LeadSearch;
    
    if (request.searchId) {
        
        searchRecord = await leadSearchService.findOne({ id: request.searchId, workspaceId });
        
        if (!searchRecord) {
            throw new NotfoundError(ErrorMessage.EntityNotFound);
        }
    } else {
        
        const searchHash = generateSearchHash(request.filters);
        searchRecord = new LeadSearch();
        searchRecord.workspaceId = workspaceId;
        searchRecord.searchHash = searchHash;
        searchRecord.filters = request.filters;
        searchRecord.createdById = userId;
    }

    searchRecord.name = request.name;
    searchRecord.description = request.description;
    searchRecord.isSaved = true;
    searchRecord.updatedById = userId;

    await leadSearchService.update({ id: searchRecord.id }, searchRecord);
    return searchRecord;
};

interface GetSavedSearchesOptions {
    page: number;
    limit: number;
    search?: string;
}


export const GetSavedSearches = async (userId: string, workspaceId: string, options: GetSavedSearchesOptions) => {
    const member = await GetMyWorkspace(userId, workspaceId);
    if (!member) {
        throw new UnauthorizedError(ErrorMessage.UnableToAuthorize);
    }
    
    if (!HasPermission(member, WorkspacePermission.ReadWorkspace)) {
        throw new UnauthorizedError(ErrorMessage.UnableToAuthorize);
    }
    
    const { page, limit, search } = options;
    const leadSearchService = new LeadSearchService();

    const result = await leadSearchService.getSavedSearches(workspaceId, { page, limit, search });

    return {
        searches: result.searches,
        totalCount: result.total,
        hasNextPage: (page * limit) < result.total
    };
};

export const GetSavedSearch = async (userId: string, workspaceId: string, searchId: string) => {
    const member = await GetMyWorkspace(userId, workspaceId);
    if (!member) {
        throw new UnauthorizedError(ErrorMessage.UnableToAuthorize);
    }
    
    if (!HasPermission(member, WorkspacePermission.ReadWorkspace)) {
        throw new UnauthorizedError(ErrorMessage.UnableToAuthorize);
    }
    
    const leadSearchService = new LeadSearchService();
    const search = await leadSearchService.findOne({ id: searchId, workspaceId, isSaved: true });
    
    if (!search) {
        throw new NotfoundError(ErrorMessage.EntityNotFound);
    }
    
    return search;
};

export const UpdateSavedSearch = async (userId: string, workspaceId: string, searchId: string, updateData: SearchUpdateData) => {
    const member = await GetMyWorkspace(userId, workspaceId);
    if (!member) {
        throw new UnauthorizedError(ErrorMessage.UnableToAuthorize);
    }
    
    if (!HasPermission(member, WorkspacePermission.UpdateWorkspace)) {
        throw new UnauthorizedError(ErrorMessage.UnableToAuthorize);
    }
    
    const leadSearchService = new LeadSearchService();
    const search = await leadSearchService.findOne({ id: searchId, workspaceId });
    
    if (!search) {
        throw new NotfoundError(ErrorMessage.EntityNotFound);
    }
    
    Object.assign(search, updateData);
    search.updatedById = userId;
    
    return await leadSearchService.update({ id: searchId }, search);
};

export const DeleteSavedSearch = async (userId: string, workspaceId: string, searchId: string) => {

    const member = await GetMyWorkspace(userId, workspaceId);
    if (!member) {
        throw new UnauthorizedError(ErrorMessage.UnableToAuthorize);
    }
    
    if (!HasPermission(member, WorkspacePermission.UpdateWorkspace)) {
        throw new UnauthorizedError(ErrorMessage.UnableToAuthorize);
    }
    
    const leadSearchService = new LeadSearchService();
    
    
    const search = await leadSearchService.findByWorkspaceAndSearchId(workspaceId, searchId);
    if (!search) {
        throw new NotfoundError(ErrorMessage.EntityNotFound);
    }
    
    
    const result = await leadSearchService.removeSearch(searchId);
    
    if (!result) {
        throw new NotfoundError(ErrorMessage.EntityNotFound);
    }
};


export const DeleteLead = async (userId: string, workspaceId: string, leadId: string) => {
    
    const member = await GetMyWorkspace(userId, workspaceId);
    if (!HasPermission(member, WorkspacePermission.UpdateWorkspace)) {
        throw new UnauthorizedError(ErrorMessage.UnableToAuthorize);
    }
    
    const leadService = new LeadService();
    
    
    const lead = await leadService.findOne({ id: leadId, workspaceId });
    if (!lead) {
        throw new NotfoundError(ErrorMessage.EntityNotFound);
    }
    
    const result = await leadService.removeLead(leadId);
    
    if (!result) {
        throw new NotfoundError(ErrorMessage.EntityNotFound);
    }
    
    return { success: true, deletedAt: new Date() };
};

interface ExecuteSavedSearchOptions {
    pagination: SearchPagination;
}

export const ExecuteSavedSearch = async (userId: string, workspaceId: string, searchId: string, options: ExecuteSavedSearchOptions) => {

    const member = await GetMyWorkspace(userId, workspaceId);
    if (!member) {
        throw new UnauthorizedError(ErrorMessage.UnableToAuthorize);
    }
    
    if (!HasPermission(member, WorkspacePermission.ReadWorkspace)) {
        throw new UnauthorizedError(ErrorMessage.UnableToAuthorize);
    }
    
    const search = await GetSavedSearch(userId, workspaceId, searchId);
    
    return await SearchPeopleLeads(userId, workspaceId, {
        filters: search.filters,
        pagination: options.pagination,
        excludeMyLeads: false
    });
};

export const GetLead = async (userId: string, workspaceId: string, leadId: string) => {

    const member = await GetMyWorkspace(userId, workspaceId);
    if (!member) {
        throw new UnauthorizedError(ErrorMessage.UnableToAuthorize);
    }
    
    if (!HasPermission(member, WorkspacePermission.ReadWorkspace)) {
        throw new UnauthorizedError(ErrorMessage.UnableToAuthorize);
    }
    
    const leadService = new LeadService();
    const lead = await leadService.findOne({ id: leadId, workspaceId });
    
    if (!lead) {
        throw new NotfoundError(ErrorMessage.EntityNotFound);
    }
    
    return lead;
};

export const UpdateLead = async (userId: string, workspaceId: string, leadId: string, updateData: LeadUpdateData) => {
    const member = await GetMyWorkspace(userId, workspaceId);
    if (!member) {
        throw new UnauthorizedError(ErrorMessage.UnableToAuthorize);
    }
    
    if (!HasPermission(member, WorkspacePermission.UpdateWorkspace)) {
        throw new UnauthorizedError(ErrorMessage.UnableToAuthorize);
    }
    
    const leadService = new LeadService();
    const lead = await leadService.findOne({ id: leadId, workspaceId });
    
    if (!lead) {
        throw new NotfoundError(ErrorMessage.EntityNotFound);
    }
    
    Object.assign(lead, updateData);
    lead.updatedById = userId;
    
    return await leadService.update({ id: leadId }, lead);
};

export const VoteLead = async (userId: string, workspaceId: string, leadId: string, request: LeadVoteRequest) => {
    const member = await GetMyWorkspace(userId, workspaceId);
    if (!member) {
        throw new UnauthorizedError(ErrorMessage.UnableToAuthorize);
    }
    
    if (!HasPermission(member, WorkspacePermission.UpdateWorkspace)) {
        throw new UnauthorizedError(ErrorMessage.UnableToAuthorize);
    }
    
    const lead = await GetLead(userId, workspaceId, leadId);
    
    if (!lead.isUnlocked) {
        throw new UnauthorizedError('Lead must be unlocked before voting');
    }
    
    if (!request.feedback || request.feedback.trim().length === 0) {
        throw new RequiredParameterError('Feedback is required for voting');
    }
    
    const currentMeta: LeadMeta = (lead.meta as LeadMeta) || {};
    
    const existingVotes: LeadVoteFeedback[] = currentMeta.votes || [];
    
    const newVote: LeadVoteFeedback = {
        userId,
        vote: request.vote,
        feedback: request.feedback.trim(),
        feedbackType: request.feedbackType,
        votedAt: new Date()
    };
    
    const newMeta: LeadMeta = {
        ...currentMeta,
        votes: [...existingVotes, newVote]
    };
    
    lead.meta = newMeta;
    
    const leadService = new LeadService();
    return await leadService.update({ id: lead.id }, lead);
};

export const GetVoteFeedbackOptions = () => {
    return PREDEFINED_FEEDBACK_OPTIONS;
};

export const BulkUnlockLeads = async (userId: string, workspaceId: string, leadIds: string[], request: BulkUnlockRequest) => {
    const member = await GetMyWorkspace(userId, workspaceId);
    if (!member) {
        throw new UnauthorizedError(ErrorMessage.UnableToAuthorize);
    }
    
    if (!HasPermission(member, WorkspacePermission.UpdateWorkspace)) {
        throw new UnauthorizedError(ErrorMessage.UnableToAuthorize);
    }
    
    const leadService = new LeadService();
    const leadUnlockService = new LeadUnlockService();
    
    const leads = await leadService.findByWorkspaceAndIds(workspaceId, leadIds);
    const leadsNeedingEnrichment = leads.filter(lead => !lead.email || !lead.normalizedData?.phone);
    
    if (leadsNeedingEnrichment.length > 0) {
        const peopleLeads = leadsNeedingEnrichment.filter(lead => lead.type === LeadType.Person);
        const companyLeads = leadsNeedingEnrichment.filter(lead => lead.type === LeadType.Company);
        
        if (peopleLeads.length > 0) {
            const emails = peopleLeads.map(lead => lead.email).filter(Boolean);
            if (emails.length > 0) {
                try {
                    const enrichedPeople = await ExecuteWorkspaceIntegrationActionNoAuth(
                        workspaceId,
                        'apollo',
                        {
                            name: 'bulkPeopleEnrichment',
                            type: 'action',
                            mode: 'run',
                            propsValue: { emails: emails.slice(0, 10) } // Apollo limits to 10
                        },
                        null
                    );
                    
                    for (const enrichedPerson of enrichedPeople.result.people) {
                        const lead = peopleLeads.find(l => l.email === enrichedPerson.email);
                        if (lead) {
                            await leadService.update({ id: lead.id }, {
                                email: enrichedPerson.email,
                                normalizedData: { ...lead.normalizedData, phone: enrichedPerson.phone },
                                meta: { ...lead.meta, enriched: true }
                            });
                        }
                    }
                } catch (error) {
                    consoleLog('Bulk people enrichment failed:', error);
                }
            }
        }
        
        if (companyLeads.length > 0) {
            const domains = companyLeads.map(lead => lead.companyDomain).filter(Boolean);
            if (domains.length > 0) {
                try {
                    const enrichedCompanies = await ExecuteWorkspaceIntegrationActionNoAuth(
                        workspaceId,
                        'apollo',
                        {
                            name: 'bulkOrganizationEnrichment',
                            type: 'action',
                            mode: 'run',
                            propsValue: { domains: domains.slice(0, 10) } // Apollo limits to 10
                        },
                        null
                    );
                    
                    for (const enrichedCompany of enrichedCompanies.result.organizations) {
                        const lead = companyLeads.find(l => l.companyDomain === enrichedCompany.domain);
                        if (lead) {
                            await leadService.update({ id: lead.id }, {
                                meta: { ...lead.meta, enriched: true, companyData: enrichedCompany }
                            });
                        }
                    }
                } catch (error) {
                    consoleLog('Bulk company enrichment failed:', error);
                }
            }
        }
    }
    
    const results = [];
    
    for (const leadId of leadIds) {
        try {
            const result = await UnlockLead(userId, workspaceId, leadId, request);
            results.push({ leadId, success: true, result });
        } catch (error) {
            results.push({ leadId, success: false, error: error.message });
        }
    }
    
    return {
        total: leadIds.length,
        successful: results.filter(r => r.success).length,
        failed: results.filter(r => !r.success).length,
        results
    };
};

export interface CreateLeadInApolloRequest {
    firstName: string;
    lastName: string;
    email: string;
    companyDomain?: string;
    jobTitle?: string;
    meta?: Record<string, unknown>;
}

export const CreateLeadInApollo = async (userId: string, workspaceId: string, leadData: CreateLeadInApolloRequest) => {
    
    validateWorkspaceId(workspaceId);
    if (!leadData.firstName || typeof leadData.firstName !== 'string' || leadData.firstName.trim().length === 0) {
        throw new RequiredParameterError('firstName is required and must be a non-empty string');
    }
    if (!leadData.lastName || typeof leadData.lastName !== 'string' || leadData.lastName.trim().length === 0) {
        throw new RequiredParameterError('lastName is required and must be a non-empty string');
    }
    if (!leadData.email || typeof leadData.email !== 'string' || leadData.email.trim().length === 0) {
        throw new RequiredParameterError('email is required and must be a non-empty string');
    }
    
    const member = await GetMyWorkspace(userId, workspaceId);
    if (!member) {
        throw new UnauthorizedError(ErrorMessage.UnableToAuthorize);
    }

    if (!HasPermission(member, WorkspacePermission.UpdateWorkspace)) {
        throw new UnauthorizedError(ErrorMessage.UnableToAuthorize);
    }
    
    const leadService = new LeadService();
    
    try {
        const apolloContact = await ExecuteWorkspaceIntegrationActionNoAuth(
            workspaceId,
            'apollo',
            {
                name: 'createContact',
                type: 'action',
                mode: 'run',
                propsValue: {
                    first_name: leadData.firstName,
                    last_name: leadData.lastName,
                    email: leadData.email
                }
            },
            null
        );
        
        if (!apolloContact?.result?.contact?.id) {
            throw new BadRequestError('Invalid response from Apollo createContact action');
        }
        
        const normalizedData: NormalizedLeadData = {
            id: apolloContact.result.contact.id,
            name: `${leadData.firstName} ${leadData.lastName}`,
            email: leadData.email,
            phone: undefined,
            jobTitle: leadData.jobTitle,
            company: undefined,
            companyDomain: leadData.companyDomain,
            linkedinUrl: undefined,
            photoUrl: undefined,
            location: undefined,
            isEmailVisible: true,
            isPhoneVisible: false,
            confidence: undefined
        };
        
        validateNormalizedLeadData(normalizedData);
        
        const lead = await leadService.createLead({
            apolloId: apolloContact.result.contact.id,
            type: LeadType.Person,
            source: LeadSource.Manual,
            apolloData: apolloContact.result.contact,
            normalizedData,
            searchHashes: [],
            email: leadData.email,
            name: normalizedData.name,
            companyDomain: leadData.companyDomain,
            createdById: userId,
            workspaceId
        });
        
        return {
            lead,
            apolloContact: apolloContact.result.contact,
            success: true
        };
    } catch (error) {
        throw new ServerProcessingError(`Failed to create lead in Apollo: ${error.message}`);
    }
};

 
export const SearchCompanyLeadsWithBulkEnrichment = async (userId: string, workspaceId: string, request: SearchLeadsRequest): Promise<SearchLeadsResponse> => {
 
    validateWorkspaceId(workspaceId);
    validateSearchFilters(request.filters);
    validatePagination(request.pagination);
    
    const member = await GetMyWorkspace(userId, workspaceId);
    if (!member) {
        throw new UnauthorizedError(ErrorMessage.UnableToAuthorize);
    }

    if (!HasPermission(member, WorkspacePermission.ReadWorkspace)) {
        throw new UnauthorizedError(ErrorMessage.UnableToAuthorize);
    }

    const { filters, pagination, excludeMyLeads } = request;
    
    const MAX_BULK_ENRICHMENT = 10;
    
    if (filters.customFilters?.companyDomains && Array.isArray(filters.customFilters.companyDomains) && filters.customFilters.companyDomains.length > 0) {
        try {
            const enrichedCompanies = await ExecuteWorkspaceIntegrationActionNoAuth(
                workspaceId,
                'apollo',
                {
                    name: 'bulkOrganizationEnrichment',
                    type: 'action',
                    mode: 'run',
                    propsValue: { domains: filters.customFilters.companyDomains.slice(0, MAX_BULK_ENRICHMENT) }
                },
                null
            );
            
            if (!enrichedCompanies?.result?.organizations || !Array.isArray(enrichedCompanies.result.organizations)) {
                throw new BadRequestError('Invalid response from Apollo bulkOrganizationEnrichment action');
            }
            
            const enrichedLeads = enrichedCompanies.result.organizations.map((company: ApolloCompanyData) => ({
                apolloId: company.id,
                type: LeadType.Company,
                name: company.name,
                companyDomain: company.domain,
                source: LeadSource.Apollo,
                meta: {
                    industry: company.industry,
                    size: company.size,
                    location: company.location,
                    technologies: company.technologies,
                    funding: company.funding,
                    enriched: true
                },
                workspaceId
            }));
            
            await processAndStoreLeads({workspaceId, apolloResults: enrichedLeads, searchHash: '', userId, leadType: LeadType.Company});
            
            const processedLeads = await processLeadsForResponse(enrichedLeads, excludeMyLeads, userId, workspaceId);
            
            return {
                leads: processedLeads,
                totalCount: enrichedLeads.length,
                hasNextPage: false,
                searchId: null,
                filters,
                metadata: { apolloRequestId: 'bulk_enrichment', apolloCreditsUsed: 1, processingTimeMs: 0, resultQuality: 'high', dataFreshness: new Date() }
            };
        } catch (error) {
            consoleLog('Bulk company enrichment failed:', error);
        }
    }
    
    return SearchLeadsByType(userId, workspaceId, request, LeadType.Company);
};


export const SearchPeopleLeadsWithBulkEnrichment = async (userId: string, workspaceId: string, request: SearchLeadsRequest): Promise<SearchLeadsResponse> => {
 
    validateWorkspaceId(workspaceId);
    validateSearchFilters(request.filters);
    validatePagination(request.pagination);
    
    const member = await GetMyWorkspace(userId, workspaceId);
    if (!member) {
        throw new UnauthorizedError(ErrorMessage.UnableToAuthorize);
    }

    if (!HasPermission(member, WorkspacePermission.ReadWorkspace)) {
        throw new UnauthorizedError(ErrorMessage.UnableToAuthorize);
    }

    const { filters, pagination, excludeMyLeads } = request;
    
    const MAX_BULK_ENRICHMENT = 10;
    
    if (filters.customFilters?.emails && Array.isArray(filters.customFilters.emails) && filters.customFilters.emails.length > 0) {
        try {
            const enrichedPeople = await ExecuteWorkspaceIntegrationActionNoAuth(
                workspaceId,
                'apollo',
                {
                    name: 'bulkPeopleEnrichment',
                    type: 'action',
                    mode: 'run',
                    propsValue: { emails: filters.customFilters.emails.slice(0, MAX_BULK_ENRICHMENT) }
                },
                null
            );
            
            if (!enrichedPeople?.result?.people || !Array.isArray(enrichedPeople.result.people)) {
                throw new BadRequestError('Invalid response from Apollo bulkPeopleEnrichment action');
            }
            
            const enrichedLeads = enrichedPeople.result.people.map((person: ApolloPersonData) => ({
                apolloId: person.id,
                type: LeadType.Person,
                name: person.name || `${person.first_name || ''} ${person.last_name || ''}`.trim(),
                email: person.email,
                source: LeadSource.Apollo,
                meta: {
                    jobTitle: person.title,
                    company: person.organization?.name,
                    location: person.city ? `${person.city}, ${person.state}` : person.country,
                    linkedinUrl: person.linkedin_url,
                    enriched: true
                },
                workspaceId
            }));
            
            await processAndStoreLeads({workspaceId, apolloResults: enrichedLeads, searchHash: '', userId, leadType: LeadType.Person});
            
            const processedLeads = await processLeadsForResponse(enrichedLeads, excludeMyLeads, userId, workspaceId);
            
            return {
                leads: processedLeads,
                totalCount: enrichedLeads.length,
                hasNextPage: false,
                searchId: null,
                filters,
                metadata: { apolloRequestId: 'bulk_enrichment', apolloCreditsUsed: 1, processingTimeMs: 0, resultQuality: 'high', dataFreshness: new Date() }
            };
        } catch (error) {
            consoleLog('Bulk people enrichment failed:', error);
        }
    }
    
    return SearchLeadsByType(userId, workspaceId, request, LeadType.Person);
};


export interface BulkEnrichLeadsData {
    leadIds: string[];
    enrichmentType?: UnlockType;
}

export const BulkEnrichLeads = async (userId: string, workspaceId: string, bulkEnrichData: BulkEnrichLeadsData) => {
    
    validateWorkspaceId(workspaceId);
    if (!bulkEnrichData.leadIds || !Array.isArray(bulkEnrichData.leadIds) || bulkEnrichData.leadIds.length === 0) {
        throw new RequiredParameterError('leadIds array is required and must not be empty');
    }
    
    const member = await GetMyWorkspace(userId, workspaceId);
    if (!member) {
        throw new UnauthorizedError(ErrorMessage.UnableToAuthorize);
    }

    if (!HasPermission(member, WorkspacePermission.UpdateWorkspace)) {
        throw new UnauthorizedError(ErrorMessage.UnableToAuthorize);
    }
    
    const leadService = new LeadService();
    const results = [];
    
    const leads = await leadService.findByWorkspaceAndIds(workspaceId, bulkEnrichData.leadIds);
    const peopleLeads = leads.filter(lead => lead.type === LeadType.Person);
    const companyLeads = leads.filter(lead => lead.type === LeadType.Company);
    
    if (peopleLeads.length > 0) {
        const emails = peopleLeads.map(lead => lead.email).filter(Boolean);
        if (emails.length > 0) {
            try {
                const enrichedPeople = await ExecuteWorkspaceIntegrationActionNoAuth(
                    workspaceId,
                    'apollo',
                    {
                        name: 'bulkPeopleEnrichment',
                        type: 'action',
                        mode: 'run',
                        propsValue: { emails: emails.slice(0, 10) }
                    },
                    null
                );
                
                for (const enrichedPerson of enrichedPeople.result.people) {
                    const lead = peopleLeads.find(l => l.email === enrichedPerson.email);
                    if (lead) {
                        await leadService.update({ id: lead.id }, {
                            normalizedData: { ...lead.normalizedData, phone: enrichedPerson.phone },
                            meta: { ...lead.meta, enriched: true, enrichedAt: new Date().toISOString() }
                        });
                        results.push({ leadId: lead.id, success: true, type: 'person' });
                    }
                }
            } catch (error) {
                if (error instanceof BadRequestError) {
                    throw error;
                }
     
                consoleLog('Bulk people enrichment failed:', error);
                results.push({ leadId: 'bulk_people', success: false, error: error.message, type: 'person' });
            }
        }
    }
    
    // Bulk enrich company leads
    if (companyLeads.length > 0) {
        const domains = companyLeads.map(lead => lead.companyDomain).filter(Boolean);
        if (domains.length > 0) {
            try {
                const enrichedCompanies = await ExecuteWorkspaceIntegrationActionNoAuth(
                    workspaceId,
                    'apollo',
                    {
                        name: 'bulkOrganizationEnrichment',
                        type: 'action',
                        mode: 'run',
                        propsValue: { domains: domains.slice(0, 10) }
                    },
                    null
                );
                
                for (const enrichedCompany of enrichedCompanies.result.organizations) {
                    const lead = companyLeads.find(l => l.companyDomain === enrichedCompany.domain);
                    if (lead) {
                        await leadService.update({ id: lead.id }, {
                            meta: { ...lead.meta, enriched: true, companyData: enrichedCompany, enrichedAt: new Date().toISOString() }
                        });
                        results.push({ leadId: lead.id, success: true, type: 'company' });
                    }
                }
            } catch (error) {
                if (error instanceof BadRequestError) {
                    throw error;
                }
                consoleLog('Bulk company enrichment failed:', error);
                results.push({ leadId: 'bulk_company', success: false, error: error.message, type: 'company' });
            }
        }
    }
    
    return {
        total: bulkEnrichData.leadIds.length,
        successful: results.filter(r => r.success).length,
        failed: results.filter(r => !r.success).length,
        results
    };
};

// Frontend will handle file generation and download

export const ExportLeads = async (userId: string, workspaceId: string, leadIds: string[], request: ExportRequest) => {
    const member = await GetMyWorkspace(userId, workspaceId);
    if (!member) {
        throw new UnauthorizedError(ErrorMessage.UnableToAuthorize);
    }
    
    if (!HasPermission(member, WorkspacePermission.ReadWorkspace)) {
        throw new UnauthorizedError(ErrorMessage.UnableToAuthorize);
    }
    
    const leadService = new LeadService();
    const leads = await leadService.findByWorkspaceAndIds(workspaceId, leadIds);
    
    // Validate export limits
    if (leads.length > 1000) {
        throw new InvalidParameterError('Cannot export more than 1000 leads at once');
    }
    
    // Return data for frontend to handle file generation and download
    return {
        format: request.format,
        leadCount: leads.length,
        data: leads.map(lead => ({
            id: lead.id,
            name: lead.normalizedData?.name,
            email: lead.normalizedData?.email,
            phone: lead.normalizedData?.phone,
            company: lead.normalizedData?.company,
            jobTitle: lead.normalizedData?.jobTitle,
            companyDomain: lead.normalizedData?.companyDomain,
            type: lead.type,
            source: lead.source,
            createdAt: lead.createdAt,
            ...(request.includeFullData ? { apolloData: lead.apolloData } : {})
        }))
    };
};

// ✅ INTERFACE FOR AddLeadToSegment FUNCTION
export interface AddLeadToSegmentData {
    name: string;
    databaseId?: string;
}

// Add lead to segments 
export const AddLeadToSegment = async ( userId: string, workspaceId: string, leadId: string,segmentData: AddLeadToSegmentData) => {
   
    const lead = await GetLead(userId, workspaceId, leadId);
    
   
    const member = await GetMyWorkspace(userId, workspaceId);
    if (!HasPermission(member, WorkspacePermission.UpdateWorkspace)) {
        throw new UnauthorizedError(ErrorMessage.UnableToAuthorize);
    }
    
    // Use existing database system to create a segment
    const { databases } = await GetMyDatabases(userId, workspaceId);
    
    // Find or create a leads database
    let leadsDatabase = databases.find(db => db.database.name === 'Leads Segment');
    let databaseId: string;
    
    if (!leadsDatabase) {
        // Create a new database for leads segments
        const newDatabase = await CreateDatabase(userId, workspaceId, {
            name: 'Leads Segment'
        });
        databaseId = newDatabase.database.database.id;
    } else {
        databaseId = leadsDatabase.database.id;
    }
    
    // Add lead to the segment database using the correct structure
    const segmentRecord = await AddRecords(userId, workspaceId, databaseId, {
        valuesList: [{
            'Lead ID': leadId,
            'Segment Name': segmentData.name,
            'Lead Name': lead.normalizedData.name,
            'Company': lead.normalizedData.company || '',
            'Job Title': lead.normalizedData.jobTitle || '',
            'Added Date': new Date().toISOString()
        }],
        onDuplicate: OnDuplicateAction.Update
    });
    
    return {
        success: true,
        segmentId: segmentRecord.records[0].id,
        databaseId: databaseId,
        addedAt: new Date()
    };
};


export interface AddLeadToWorkflowData {
    workflowId: string;
    triggerType?: 'manual' | 'immediate';
}

export const AddLeadToWorkflow = async ( userId: string, workspaceId: string, leadId: string, workflowData: AddLeadToWorkflowData) => {
    // Validate lead exists
    const lead = await GetLead(userId, workspaceId, leadId);
    
    // Check permissions
    const member = await GetMyWorkspace(userId, workspaceId);
    if (!HasPermission(member, WorkspacePermission.UpdateWorkspace)) {
        throw new UnauthorizedError(ErrorMessage.UnableToAuthorize);
    }
    
    // Use existing workflow system
    const workflows = await GetWorkflows(userId, workspaceId, { id: workflowData.workflowId });
    
    // Find the workflow
    const workflow = workflows.find(w => w.id === workflowData.workflowId);
    if (!workflow) {
        throw new NotfoundError('Workflow not found');
    }
    
    // Check if workflow can be manually triggered
    if (workflow.triggerType !== 'OnDemand_Callable') {
        throw new BadRequestError('Workflow must be OnDemand_Callable to be manually triggered');
    }
    
    // Create a workflow instance with lead data
    const workflowInstance = await CreateWorkflowInstance(userId, workspaceId, workflowData.workflowId, {
        data: {
            leadId,
            leadData: lead,
            triggerSource: 'lead_management'
        }
    });
    
    return {
        success: true,
        workflowId: workflowData.workflowId,
        workflowInstanceId: workflowInstance.id,
        triggeredAt: new Date()
    };
};


export interface AddLeadToDatabaseData {
    targetDatabaseId: string;
}

// Add lead to external database 
export const AddLeadToDatabase = async ( userId: string, workspaceId: string, leadId: string, databaseData: AddLeadToDatabaseData) => {
   
    const lead = await GetLead(userId, workspaceId, leadId);
    
   
    const member = await GetMyWorkspace(userId, workspaceId);
    if (!HasPermission(member, WorkspacePermission.UpdateWorkspace)) {
        throw new UnauthorizedError(ErrorMessage.UnableToAuthorize);
    }
    
   
    const { database: targetDatabase } = await GetMyDatabase(userId, workspaceId, databaseData.targetDatabaseId);
    
   
    const recordValues: RecordValues = {};
    
    // Auto-map common fields based on column names
    if (targetDatabase.database.definition && targetDatabase.database.definition.columnsMap) {
        Object.keys(targetDatabase.database.definition.columnsMap).forEach(columnId => {
            const column = targetDatabase.database.definition.columnsMap[columnId];
            // Access column name safely - columns have different structures
            const columnName = typeof column === 'object' && column !== null && 'name' in column 
                ? (column as { name: string }).name 
                : columnId;
            const columnNameLower = columnName.toLowerCase();
            
            if (columnNameLower.includes('name') && !columnNameLower.includes('company')) {
                recordValues[columnName] = lead.normalizedData.name;
            } else if (columnNameLower.includes('company')) {
                recordValues[columnName] = lead.normalizedData.company || '';
            } else if (columnNameLower.includes('email')) {
                recordValues[columnName] = lead.normalizedData.email || '';
            } else if (columnNameLower.includes('phone')) {
                recordValues[columnName] = lead.normalizedData.phone || '';
            } else if (columnNameLower.includes('job') || columnNameLower.includes('title')) {
                recordValues[columnName] = lead.normalizedData.jobTitle || '';
            } else if (columnNameLower.includes('date') || columnNameLower.includes('created')) {
                recordValues[columnName] = new Date().toISOString();
            }
        });
    }
    
    // Add lead to target database
    const result = await AddRecords(userId, workspaceId, databaseData.targetDatabaseId, {
        valuesList: [recordValues],
        onDuplicate: OnDuplicateAction.Update
    });
    
    return {
        success: true,
        databaseId: databaseData.targetDatabaseId,
        recordId: result.records[0].id,
        syncedAt: new Date()
    };
};

    export interface SendEmailToLeadData {
    subject: string;
    body: string;
    template?: string;
}

// Send email to lead (using existing email service)
export const SendEmailToLead = async ( userId: string, workspaceId: string,leadId: string,emailData: SendEmailToLeadData) => {
    // Validate lead exists
    const lead = await GetLead(userId, workspaceId, leadId);
    
    // Check if lead is unlocked
    if (!lead.isUnlocked) {
        throw new UnauthorizedError('Lead must be unlocked before sending email');
    }
    
    // Check permissions
    const member = await GetMyWorkspace(userId, workspaceId);
    if (!HasPermission(member, WorkspacePermission.UpdateWorkspace)) {
        throw new UnauthorizedError(ErrorMessage.UnableToAuthorize);
    }
    
    
    const emailResult = await SendEmailWithContent(
        { email: lead.normalizedData.email, name: lead.normalizedData.name },
        emailData.subject,
        emailData.body,
        null, 
        null, 
        true, 
        undefined, 
        undefined,
        undefined, 
        [],
        true,
        [], // cc
        [] // bcc
    );
    
    return {
        success: true,
        emailId: emailResult,
        sentAt: new Date()
    };
};


export const GetLeadsBySearchHashes = async (userId: string, workspaceId: string, searchHashes: string[], pagination?: SearchPagination) => {
    // Check permissions
    const member = await GetMyWorkspace(userId, workspaceId);
    if (!member) {
        throw new UnauthorizedError(ErrorMessage.UnableToAuthorize);
    }
    
    if (!HasPermission(member, WorkspacePermission.ReadWorkspace)) {
        throw new UnauthorizedError(ErrorMessage.UnableToAuthorize);
    }
    
    if (searchHashes.length === 0) {
        return {
            leads: [],
            totalCount: 0,
            hasNextPage: false
        };
    }
    
    const leadService = new LeadService();

    const leads = await leadService.findByWorkspaceAndSearchHashes(workspaceId, searchHashes);
    
    // If no pagination provided, return all results
    if (!pagination) {
        return {
            leads,
            totalCount: leads.length,
            hasNextPage: false
        };
    }
    
    // Apply pagination
    const { page, limit } = pagination;
    const startIndex = (page - 1) * limit;
    const endIndex = startIndex + limit;
    const paginatedLeads = leads.slice(startIndex, endIndex);
    
    return {
        leads: paginatedLeads,
        totalCount: leads.length,
        hasNextPage: (page * limit) < leads.length
    };
};


export const IsLeadUnlocked = async (userId: string, workspaceId: string, leadId: string): Promise<boolean> => {
    // Check permissions
    const member = await GetMyWorkspace(userId, workspaceId);
    if (!HasPermission(member, WorkspacePermission.ReadWorkspace)) {
        throw new UnauthorizedError(ErrorMessage.UnableToAuthorize);
    }
    
    const leadUnlockService = new LeadUnlockService();
    
    // Use new service method
    return await leadUnlockService.isLeadUnlocked(workspaceId, leadId);
};



// Validate search filters
function validateSearchFilters(filters: SearchFilters): void {
    if (!filters) {
        throw new RequiredParameterError('Search filters are required');
    }
    
    // Check if search has meaningful criteria
    const hasPersonCriteria = filters.person && (
        (filters.person.jobTitles && filters.person.jobTitles.length > 0) ||
        (filters.person.location && filters.person.location.length > 0) ||
        (filters.person.managementLevel && filters.person.managementLevel.length > 0) ||
        (filters.person.jobFunction && filters.person.jobFunction.length > 0) ||
        (filters.person.seniority && filters.person.seniority.length > 0) ||
        (filters.person.departments && filters.person.departments.length > 0) ||
        (filters.person.skills && filters.person.skills.length > 0)
    );
    
    const hasCompanyCriteria = filters.company && (
        // Keywords alone should be sufficient for company search
        (filters.company.keywords && filters.company.keywords.length > 0) ||
        // OR any other criteria
        ((filters.company.industry && filters.company.industry.length > 0) || 
         (filters.company.industries && filters.company.industries.length > 0)) ||
        (filters.company.companySize && filters.company.companySize.length > 0) ||
        (filters.company.companyType && filters.company.companyType.length > 0) ||
        (filters.company.location && filters.company.location.length > 0) ||
        (filters.company.technologies && filters.company.technologies.length > 0) ||
        (filters.company.revenue && (filters.company.revenue.min !== undefined || filters.company.revenue.max !== undefined)) ||
        (filters.company.employees && (filters.company.employees.min !== undefined || filters.company.employees.max !== undefined)) ||
        (filters.company.foundedYear && (filters.company.foundedYear.min !== undefined || filters.company.foundedYear.max !== undefined))
    );
    
    const hasSignalsCriteria = filters.signals && (
        filters.signals.newTechnologies === true ||
        filters.signals.fundingEvents === true
    );
    
    const hasCustomCriteria = filters.customFilters && (
        Object.keys(filters.customFilters).some(key => key !== 'searchQuery' && filters.customFilters[key])
    );
    
    if (!hasPersonCriteria && !hasCompanyCriteria && !hasSignalsCriteria && !hasCustomCriteria) {
        throw new BadRequestError('Search must have at least one meaningful filter criteria. Please provide keywords, job titles, location, industry, company size, or other specific search parameters.');
    }
    
    // Validate person filters if present
    if (filters.person) {
        if (filters.person.jobTitles && !Array.isArray(filters.person.jobTitles)) {
            throw new BadRequestError('Job titles must be an array');
        }
        if (filters.person.location && !Array.isArray(filters.person.location)) {
            throw new BadRequestError('Location must be an array');
        }
        if (filters.person.managementLevel && !Array.isArray(filters.person.managementLevel)) {
            throw new BadRequestError('Management level must be an array');
        }
        if (filters.person.jobFunction && !Array.isArray(filters.person.jobFunction)) {
            throw new BadRequestError('Job function must be an array');
        }
        if (filters.person.seniority && !Array.isArray(filters.person.seniority)) {
            throw new BadRequestError('Seniority must be an array');
        }
        if (filters.person.departments && !Array.isArray(filters.person.departments)) {
            throw new BadRequestError('Departments must be an array');
        }
        if (filters.person.skills && !Array.isArray(filters.person.skills)) {
            throw new BadRequestError('Skills must be an array');
        }
    }
    
    // Validate company filters if present
    if (filters.company) {
        if (filters.company.industry && !Array.isArray(filters.company.industry)) {
            throw new BadRequestError('Industry must be an array');
        }
        if (filters.company.industries && !Array.isArray(filters.company.industries)) {
            throw new BadRequestError('Industries must be an array');
        }
        if (filters.company.companySize && !Array.isArray(filters.company.companySize)) {
            throw new BadRequestError('Company size must be an array');
        }
        if (filters.company.companyType && !Array.isArray(filters.company.companyType)) {
            throw new BadRequestError('Company type must be an array');
        }
        if (filters.company.location && !Array.isArray(filters.company.location)) {
            throw new BadRequestError('Company location must be an array');
        }
        if (filters.company.technologies && !Array.isArray(filters.company.technologies)) {
            throw new BadRequestError('Technologies must be an array');
        }
        if (filters.company.keywords && !Array.isArray(filters.company.keywords)) {
            throw new BadRequestError('Keywords must be an array');
        }
    }
}

// Validate pagination
function validatePagination(pagination: SearchPagination): void {
    if (!pagination) {
        throw new RequiredParameterError('Pagination is required');
    }
    
    if (typeof pagination.page !== 'number' || pagination.page < 1) {
        throw new BadRequestError('Page must be a positive number');
    }
    
    if (typeof pagination.limit !== 'number' || pagination.limit < 1 || pagination.limit > 50) {
        throw new BadRequestError('Limit must be between 1 and 50');
    }
}

// Validate lead ID format
function validateLeadId(leadId: string): void {
    if (!leadId || typeof leadId !== 'string' || leadId.trim().length === 0) {
        throw new RequiredParameterError('Lead ID is required');
    }
    
    // UUID format validation (basic)
    const uuidRegex = /^[0-9a-f]{8}-[0-9a-f]{4}-[1-5][0-9a-f]{3}-[89ab][0-9a-f]{3}-[0-9a-f]{12}$/i;
    if (!uuidRegex.test(leadId)) {
        throw new BadRequestError('Invalid lead ID format');
    }
}

// Validate workspace ID format
function validateWorkspaceId(workspaceId: string): void {
    if (!workspaceId || typeof workspaceId !== 'string' || workspaceId.trim().length === 0) {
        throw new RequiredParameterError('Workspace ID is required');
    }
    
    // UUID format validation (basic)
    const uuidRegex = /^[0-9a-f]{8}-[0-9a-f]{4}-[1-5][0-9a-f]{3}-[89ab][0-9a-f]{3}-[0-9a-f]{12}$/i;
    if (!uuidRegex.test(workspaceId)) {
        throw new BadRequestError('Invalid workspace ID format');
    }
}

// Validate email format
function validateEmail(email: string): void {
    if (email && typeof email === 'string') {
        const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
        if (!emailRegex.test(email)) {
            throw new BadRequestError('Invalid email format');
        }
    }
}

// Validate phone format
function validatePhone(phone: string): void {
    if (phone && typeof phone === 'string') {
        const phoneRegex = /^[\+]?[1-9][\d]{0,15}$/;
        if (!phoneRegex.test(phone.replace(/[\s\-\(\)]/g, ''))) {
            throw new BadRequestError('Invalid phone format');
        }
    }
}

// Validate normalized lead data
function validateNormalizedLeadData(data: NormalizedLeadData): void {
    if (!data) {
        throw new RequiredParameterError('Normalized lead data is required');
    }
    
    if (!data.name || typeof data.name !== 'string' || data.name.trim().length === 0) {
        throw new BadRequestError('Lead name is required');
    }
    
    if (data.name.length > 255) {
        throw new BadRequestError('Lead name cannot exceed 255 characters');
    }
    
    if (data.email) {
        validateEmail(data.email);
    }
    
    if (data.phone) {
        validatePhone(data.phone);
    }
    
    if (data.company && data.company.length > 255) {
        throw new BadRequestError('Company name cannot exceed 255 characters');
    }
    
    if (data.companyDomain && data.companyDomain.length > 255) {
        throw new BadRequestError('Company domain cannot exceed 255 characters');
    }
    
    if (data.jobTitle && data.jobTitle.length > 255) {
        throw new BadRequestError('Job title cannot exceed 255 characters');
    }
}

// Validate Apollo data structure
function validateApolloData(data: ApolloPersonData | ApolloCompanyData, type: LeadType): void {
    console.log(`🔍 [VALIDATION DEBUG] 🔍 STARTING VALIDATION:`);
    console.log(`🔍 [VALIDATION DEBUG] - Data type: ${type}`);
    console.log(`🔍 [VALIDATION DEBUG] - Data received:`, JSON.stringify(data, null, 2));
    console.log(`🔍 [VALIDATION DEBUG] - Has data: ${!!data}`);
    console.log(`🔍 [VALIDATION DEBUG] - Data keys: ${Object.keys(data || {}).join(', ')}`);
    
    if (!data) {
        console.log(`🔍 [VALIDATION DEBUG] ❌ FAILED: Apollo data is required`);
        throw new RequiredParameterError('Apollo data is required');
    }
    
    if (type === LeadType.Person) {
        const personData = data as ApolloPersonData;
        console.log(`🔍 [VALIDATION DEBUG] - Validating as Person`);
        console.log(`🔍 [VALIDATION DEBUG] - Person ID: ${personData.id}`);
        if (!personData.id) {
            console.log(`🔍 [VALIDATION DEBUG] ❌ FAILED: Apollo person ID is required`);
            throw new BadRequestError('Apollo person ID is required');
        }
        console.log(`🔍 [VALIDATION DEBUG] ✅ Person validation passed`);
    } else if (type === LeadType.Company) {
        const companyData = data as ApolloCompanyData;
        console.log(`🔍 [VALIDATION DEBUG] - Validating as Company`);
        console.log(`🔍 [VALIDATION DEBUG] - Company ID: ${companyData.id}`);
        console.log(`🔍 [VALIDATION DEBUG] - Company Name: ${companyData.name}`);
        console.log(`🔍 [VALIDATION DEBUG] - Company Name type: ${typeof companyData.name}`);
        console.log(`🔍 [VALIDATION DEBUG] - Company Name length: ${companyData.name?.length || 'undefined'}`);
        
        if (!companyData.id) {
            console.log(`🔍 [VALIDATION DEBUG] ❌ FAILED: Apollo company ID is required`);
            throw new BadRequestError('Apollo company ID is required');
        }
        if (!companyData.name) {
            console.log(`🔍 [VALIDATION DEBUG] ❌ FAILED: Apollo company name is required`);
            throw new BadRequestError('Apollo company name is required');
        }
        console.log(`🔍 [VALIDATION DEBUG] ✅ Company validation passed`);
    }
    
    console.log(`🔍 [VALIDATION DEBUG] ✅ ALL VALIDATIONS PASSED`);
    console.log(`🔍 [VALIDATION DEBUG] ==========================================`);
}

// Common lead validation (used in our data processing)
export const validateLeadData = (data: Partial<NormalizedLeadData>): void => {
    if (data.name && data.name.length > 255) {
        throw new BadRequestError('Lead name cannot exceed 255 characters');
    }
    
    if (data.email) {
        validateEmail(data.email);
    }
    
    if (data.phone) {
        validatePhone(data.phone);
    }
    
    if (data.company && data.company.length > 255) {
        throw new BadRequestError('Company name cannot exceed 255 characters');
    }
};


const getApolloConnectionAPI = (workspaceId: string): ConnectionAPI => {
    return {
        OAuth2Configs: async () => ({
            credentials: {
                clientId: '',
                clientSecret: ''
            },
            redirectUri: ''
        }),
        delete: async () => {},
        save: async () => {},
        get: async (integration: string, id: string): Promise<IntegrationCredentials | null> => {
            if (integration === 'apollo') {
                const apolloApiKey = process.env.APOLLO_API_KEY;
                if (!apolloApiKey) {
                    throw new Error('APOLLO_API_KEY environment variable is not set');
                }
                
                return {
                    id: 'apollo-default',
                    name: 'Apollo Default Connection',
                    integration: 'apollo',
                    credentials: {
                        apiKey: apolloApiKey
                    }
                };
            }
            return null;
        }
    };
};
