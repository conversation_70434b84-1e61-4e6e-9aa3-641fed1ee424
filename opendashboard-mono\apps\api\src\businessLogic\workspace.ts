import {Workspace} from "../entity/Workspace";
import {CreateWorkspaceData, WorkspaceService} from "../service/workspace";
import {WorkspaceMember, WorkspaceMemberRole} from "../entity/WorkspaceMember";
import {User} from "../entity/User";
import {WorkspaceInvitation} from "../entity/WorkspaceInvitation";
import {CreateWorkspaceMember, WorkspaceMemberService} from "../service/workspaceMember";
import {BadRequestError, ErrorMessage, InvalidParameterError, NotfoundError, RequiredParameterError, ServerProcessingError, UnauthorizedError, UniqueConstraintError} from "../errors/AppError";
import {CreateUser, UserService} from "../service/user";
import {FileType, ProcessFormToSpaceUpload} from "./doUpload";
import {GetProfile} from "./account";
import {CreateWorkspaceInvitation, WorkspaceInvitationService} from "../service/workspaceInvitation";
import {free, WorkspacePlanPriceIdMap} from "./subscription";
import {generateToken} from "../utility/token";
import {apiUrl, appUrl, isLocal} from "../config";
import {EmailUser, SendEmailWithContent, SMTPEmailUser, sendgridTransport} from "./email";
import {Brackets, In, LessThan, Like, MoreThanOrEqual} from "typeorm";
import {WorkspaceSenderEmail} from "../entity/WorkspaceSenderEmail";
import {extractDomainFromEmail, validateEmail, validateUsername} from "../utility/validator";
import {WorkspaceDomainService} from "../service/workspaceDomain";
import {WorkspaceSenderService} from "../service/workspaceSenderEmail";
import {TokenExpiryUnit, TokenService} from "../service/token";
import {Token, TokenType} from "../entity/Token";
import {DomainConfig, DomainConfigType, WorkspaceDomain} from "../entity/WorkspaceDomain";
import {RecordType, verifyDomainRecord} from "../utility/dns";
import {arrayDeDuplicate, arrayDiff, datePlusMonths, datePlusYears, generateUUID, substituteVarsInObjects} from "@repo/app-db-utils/dist";
import {GetMyDatabases, getRecordsNoAuth, SetupInternalWorkspaceDatabases, SystemParams} from "./database";
import {WorkspaceStats} from "../entity/WorkspaceStats";
import {GetWorkspaceStats, RecalculateWorkspaceStats} from "./stats";
import {ActiveCredits, CurrentBillingCycle, GetOrCreateCoupon, initStripeCustomer, ModifyAddOnUsersInSubscription, RestoreWorkspaceLimitedFunctionality, startBillingCycle} from "./billing";
import {BillingCycle} from "../entity/BillingCycle";
import {cancelSubscription, chargeCustomerViaInvoice, createCustomerPortal, createSubscriptionCheckoutSession, stopSubscriptionAutoRenew} from "./stripe";
import {WorkspaceCreditService} from "../service/workspaceCredit";
import {datePlusDays} from "@repo/app-db-utils/dist/methods/date";
import {WorkspaceMemberSettingService} from "../service/userWorkspaceSetting";
import {broadcastActivities, broadcastWorkspaceMembersAdded} from "../socketio/workspace";
import {ActivityService, CreateActivity} from "../service/activity";
import {ActivityObjectType} from "../entity/Activity";
import {DomainService} from "../service/domain";
import {fullAuthorizedDomain, SendGrid} from "./sendgrid";
import {Domain} from "../entity/Domain";
import {ObjectType, Person} from "@repo/app-db-utils/dist/typings/common";
import {TriggerNewWorkspaceWorkflow} from "./providers/inbranded";
import {Subscription} from "../entity/Subscription";
import {SubscriptionService} from "../service/subscription";
import {Request} from "express"
import {NotificationService} from "../service/notification";
import {Notification} from "../entity/notification";
import {CreateNotifications, CreateNotificationData} from "./notification";
import {Reminder} from "../entity/reminder";
import {TemplatePurchaseService} from "../service/templatePurchase";
import {PaymentCurrency, PaymentProcessor, PurchaseStatus, TemplatePurchaseWorkspaceLicense} from "../entity/templatePurchase";
import {dbDataSource} from "../connection/db";
import {InstallTemplate, PurchaseTemplate} from "./templates";
import {TemplateOnboardingMapping} from "./data/starter";
import {CreateWorkspaceSecret, WorkspaceSecretService} from "../service/workspaceSecret";
import {SecretType, WorkspaceSecret} from "../entity/workspaceSecret";
import {SubstituteVarData, substituteVars} from "@repo/app-db-utils/dist/methods/object";
import {GetMyPages, SetupGettingStartedPage} from "./page";
import {DocumentService} from "../service/document";
import {AddAffiliateEarningOnWorkspaceCreditPurchase, AffiliateCalculateDiscountAndEarning, GetAffiliateByReferralCode, getAffiliateCalculateDiscountAndEarning} from "./affiliate";
import {consoleLog, logInfo} from "./logtail";
import {getVerifiedTransaction, initializeTransaction} from "./paystack";
import {KeyValueStore} from "../entity/WorkspaceMemberSettings";
import {getUSDNGNRate} from "./currencyConverter";
import {WorkspaceCredit} from "../entity/WorkspaceCredit";
import {WorkspaceRiskLogService} from "../service/workspaceRiskLog";
import {deleteConnection, getConnectionAPI, getConnections, getFilesAPI, getOAuth2Configs, getStoreAPI, getWebhookURL, saveConnection} from "./integrationHelpers";
import {getCoreApi} from "./integration";
import {WorkspaceIntegrationConnection} from "../entity/workspaceIntegrationConnection";
import {encodeState, RunStepParams} from "@opendashboard-inc/integration-core-api";
import {WebhookTriggerPayload} from "@opendashboard-inc/integration-core";
import {ViewType} from "@repo/app-db-utils/dist/typings/view";
import {billWorkspaceCredit, Billable} from "./billing";
import {CampaignAttachment} from "../entity/Campaign";
import { ReminderService } from "../service/reminder";
import { constructDb, constructRecordId, matchRecordId } from "@repo/app-db-utils/dist/utils/db";

export interface MyWorkspace {
    workspace: Workspace;
    workspaceMember: WorkspaceMember
    membersCount: number
    planId: string
    priceId: string
    billingCycle: BillingCycle
}

export interface MyWorkspaceMember {
    user: User;
    workspaceMember: WorkspaceMember
}

export interface MyWorkspaceInvitation {
    invitation: WorkspaceInvitation;
    workspace: Workspace;
    messageId?: string
}

export const GetWorkspaceById = async (workspaceId: string): Promise<Workspace> => {
    const service = new WorkspaceService();

    return service.findById(workspaceId)
}

export const GetWorkspaceByDomain = async (domain: string): Promise<Workspace> => {
    const service = new WorkspaceService();
    return (await service.find({domain}))[0]
}

export const GetMyWorkspaces = async (userId: string, workspaceId?: string): Promise<MyWorkspace[]> => {
    const service = new WorkspaceService();
    const repo = service.getRepository()

    const qBQuery = repo.createQueryBuilder("w")
        .addSelect("wM")
        .addSelect(qb => {
            return qb.select("COUNT(*)")
                .from(WorkspaceMember, "wM2")
                .where("wM2.workspaceId = w.id AND wM2.deletedAt IS NULL AND wM2.role != :supportUser")
        }, "membersCount")
        .addSelect('bC')
        // .addSelect(qb => {
        //     return qb.select("planId")
        //         .from(BillingCycle, "bC")
        //         .where("bC.workspaceId = w.id AND bC.deletedAt IS NULL AND bC.isActive=1")
        //         .orderBy("bC.id", "DESC")
        //         .limit(1)
        // }, "planId")
        // .addSelect(qb => {
        //     return qb.select("priceId")
        //         .from(BillingCycle, "bC")
        //         .where("bC.workspaceId = w.id AND bC.deletedAt IS NULL AND bC.isActive=1")
        //         .orderBy("bC.id", "DESC")
        //         .limit(1)
        // }, "priceId")
        .leftJoin(WorkspaceMember, "wM", "wM.workspaceId=w.id")
        .leftJoin(BillingCycle, 'bC', 'bC.workspaceId=w.id AND bC.deletedAt IS NULL AND bC.isActive=1')
        .where("wM.userId=:userId")
        .setParameters({
            userId,
            supportUser: WorkspaceMemberRole.SupportUser
            // subActive: StatusTypes.Active
        })
    if (workspaceId) {
        qBQuery.andWhere("wM.workspaceId=:workspaceId")
            .setParameters({workspaceId})
    }

    const rawResults = await qBQuery.getRawMany()

    return rawResults.map(r => {
        const result: MyWorkspace = {
            workspace: {} as Workspace,
            workspaceMember: {} as WorkspaceMember,
            membersCount: Number(r['membersCount']),
            planId: '',
            priceId: '',
            billingCycle: {} as BillingCycle
        }
        for (let key of Object.keys(r)) {
            const [pre, field] = key.split("_")
            if (pre === "w") {
                result.workspace[field] = r[key]
            } else if (pre === "wM") {
                result.workspaceMember[field] = r[key]
            } else if (pre === "bC") {
                result.billingCycle[field] = r[key]
            }
        }
        result.planId = result.billingCycle.planId
        result.priceId = result.billingCycle.priceId
        result.billingCycle.usersQuota = Number(result.billingCycle.usersQuota)
        result.billingCycle.collaboratorsQuota = Number(result.billingCycle.collaboratorsQuota)
        result.billingCycle.recordsQuota = Number(result.billingCycle.recordsQuota)
        return result
    });
}

export const GetMyWorkspace = async (userId: string, workspaceId: string): Promise<MyWorkspace> => {
    return (await GetMyWorkspaces(userId, workspaceId))[0]
}


interface SearchResult {
    id: string;
    title?: string;
    name?: string;
    content?: string;
    path: string;
    publishedAt?: Date;
    image?: string;
    viewType?: string;
    source?: {
        databaseId?: string;
        recordId?: string;
        pageId?: string;
        viewId?: string;
        documentId?: string;
        reminderId?: string;
    };
    highlight?: {
        start: number;
        end: number;
    };
}



export const SearchMyWorkspaces = async (userId: string, workspaceId: string, query: string, page: number = 1, pageSize: number = 25) => {
    if (!workspaceId || !query) {
        throw new RequiredParameterError("workspaceId and query are required");
    }

    // Decode the query parameter to handle special characters
    const decodedQuery = decodeURIComponent(query);

    const member = await GetMyWorkspace(userId, workspaceId);
    if (!member || member.workspaceMember.role === WorkspaceMemberRole.Collaborator) {
        throw new UnauthorizedError(ErrorMessage.AuthorizationNotFound);
    }

    const results: SearchResult[] = [];
    const {databases} = await GetMyDatabases(userId, workspaceId);
    const {pages} = await GetMyPages(userId, workspaceId);
    const users = await getWorkspaceMembersAsPersons(workspaceId);

    //finds,highlight and shows a search term no matter the length
    //returns surrounding context
    //
    const searchWithContext = (text: string, term: string) => {
        if (!text) return {preview: ''};
        
        // Normalize both text and term for better matching
        const normalizedText = text.trim();
        const normalizedTerm = term.trim();
        
        // Use case-insensitive search
        const idx = normalizedText.toLowerCase().indexOf(normalizedTerm.toLowerCase());
        if (idx === -1) return {preview: normalizedText.slice(0, 100) + (normalizedText.length > 100 ? '...' : '')};
        
        const start = Math.max(0, idx - 30);
        const end = Math.min(normalizedText.length, idx + normalizedTerm.length + 30);
        const preview = (start > 0 ? '...' : '') + normalizedText.slice(start, end) + (end < normalizedText.length ? '...' : '');
        return {
            preview,
            match: {
                start: idx - start + (start > 0 ? 3 : 0),
                end: idx - start + normalizedTerm.length + (start > 0 ? 3 : 0)
            }
        };
    };

    const extractContent = (jsonString: string): string => {
        try {
            const json = JSON.parse(jsonString);
            const meaningfulValues = Object.values(json).filter(value => typeof value === 'string');
            return meaningfulValues.join(' ');
        } catch {
            return '';
        }
    };


    for (const db of databases) {
      const dbMatch = searchWithContext(db.database.name, decodedQuery);
      if (dbMatch.match) {
        results.push({
          id: db.database.id,
          title: db.database.name,
          path: `/databases/${db.database.id}`,
          publishedAt: db.database.createdAt,
          source: { databaseId: db.database.id },
          viewType: ViewType.Table,
          highlight: dbMatch.match
        });
      }


      if (db.views && Array.isArray(db.views)) {
        for (const view of db.views) {
          const viewNameLower = (view.name || '').toLowerCase();
          const queryLower = decodedQuery.toLowerCase();
          const directMatch = viewNameLower.includes(queryLower);
          const viewMatch = searchWithContext(view.name, decodedQuery);

          if (directMatch || viewMatch.match) {
            const highlight = viewMatch.match || {
              start: viewNameLower.indexOf(queryLower),
              end: viewNameLower.indexOf(queryLower) + queryLower.length
            };


            results.push({
              id: view.id,
              title: view.name,
              content: `${view.type} in ${db.database.name}`,
              path: `/databases/${db.database.id}/views/${view.id}`,
              publishedAt: view.createdAt,
              source: {
                pageId: db.page.id,
                viewId: view.id,
                databaseId: db.database.id
              },
              viewType: view.type,
              highlight: highlight
            });
          }
        }
      }
        const { isFullMatch, isPartialMatch, id } = matchRecordId(decodedQuery.toLowerCase(), db.database.definition.recordIdConfig?.recordIdPrefix, db.database.definition.recordIdConfig?.recordIdSuffix, db.database.definition.recordIdConfig?.recordIdPadLength, false);

        // i'm doing this to match partial query, like INV_, so since the Id is not yet on the string,just temperally use first record
        const extractedAutoIncrementRecordId = id ? id : 1

        const { records } = await getRecordsNoAuth(db.database);
      for (const record of records) {
          let matches = false; 
        let matchContent = '';
        let highlightInfo = null;



          //   

          if ((isFullMatch || isPartialMatch) && record.autoIncrementId === extractedAutoIncrementRecordId) {

                  matches = true;
              matchContent = record.title || constructRecordId(extractedAutoIncrementRecordId, db.database.definition.recordIdConfig?.recordIdPrefix, db.database.definition.recordIdConfig?.recordIdSuffix, db.database.definition.recordIdConfig?.recordIdPadLength);
                  highlightInfo = {
                      start: 0,
                      end: matchContent.length
                  };



              if (record.recordValues) {
                  for (const [fieldId, fieldValue] of Object.entries(record.recordValues)) {
                      let fieldValueStr = '';
                      if (typeof fieldValue === 'string') {
                          fieldValueStr = fieldValue;
                      } else if (fieldValue !== null && fieldValue !== undefined) {
                          fieldValueStr = String(fieldValue);
                      }

                      if (fieldValueStr.toLowerCase().includes(decodedQuery.toLowerCase())) {
                          matches = true;
                          matchContent = fieldValueStr;
                          const idx = fieldValueStr.toLowerCase().indexOf(decodedQuery.toLowerCase());
                          highlightInfo = {
                              start: idx,
                              end: idx + decodedQuery.length
                          };
                          break;
                      }
                  }
                }
          }
          // Search directly in each field value
          if (record.recordValues) {
              // 
          for (const [fieldId, fieldValue] of Object.entries(record.recordValues)) {
            let fieldValueStr = '';
            if (typeof fieldValue === 'string') {
              fieldValueStr = fieldValue;
            } else if (fieldValue !== null && fieldValue !== undefined) {
              fieldValueStr = String(fieldValue);
            }

            if (fieldValueStr.toLowerCase().includes(decodedQuery.toLowerCase())) {
              matches = true;
              matchContent = fieldValueStr;
              const idx = fieldValueStr.toLowerCase().indexOf(decodedQuery.toLowerCase());
              highlightInfo = {
                start: idx,
                end: idx + decodedQuery.length
              };
              break;
            }
          }
        }

        if (!matches) {
          let fullContent = record.summaryText || '';

          if (!fullContent && record.recordValues) {
            fullContent = extractContent(JSON.stringify(record.recordValues));
          }

          if (!fullContent && record.uniqueValue) {
            fullContent = record.uniqueValue;
          }

          const contentMatch = searchWithContext(fullContent, decodedQuery);
          const titleMatch = searchWithContext(record.uniqueValue || '', decodedQuery);

          if (contentMatch.match || titleMatch.match) {
            matches = true;
            matchContent = contentMatch.preview ||
            fullContent.substring(0, 100) + (fullContent.length > 100 ? '...' : '');
            highlightInfo = contentMatch.match || titleMatch.match;
          }
        }
        if (matches) {
          results.push({
            id: record.id,
            title: record.uniqueValue || `${db.database.name}`,
            content: matchContent,
            path: `/databases/${db.database.id}/records/${record.id}`,
            publishedAt: record.createdAt,
            source: {
              databaseId: db.database.id,
              recordId: record.id,
            },
            viewType: ViewType.Table,
            highlight: highlightInfo
          });
        }
      }
    }


    for (const pageItem of pages) {
        const pageMatch = searchWithContext(pageItem.page.name, decodedQuery);
        if (pageMatch.match) {
            results.push({
                id: pageItem.page.id,
                title: pageItem.page.name,
                path: `/${pageItem.page.id}`,
                publishedAt: pageItem.page.createdAt,
                source: {pageId: pageItem.page.id},
                viewType: ViewType.Document,
                highlight: pageMatch.match
            });
        }

        for (const view of pageItem.views) {
            const viewMatch = searchWithContext(view.name, decodedQuery);
            if (viewMatch.match) {
             let databaseId = null;
              if (view.definition && typeof view.definition === 'object' &&
                'databaseId' in view.definition) {
                databaseId = view.definition.databaseId;
              }

              results.push({
                id: view.id,
                title: view.name,
                content: `${view.type} in ${pageItem.page.name}`,
                path: `/${pageItem.page.id}/views/${view.id}`,
                publishedAt: view.createdAt,
                source: {
                  pageId: pageItem.page.id,
                  viewId: view.id,
                  databaseId: databaseId
                },
                viewType: view.type,
                highlight: viewMatch.match
              });
            }
        }

    }

    // Collect all view IDs from both pages and database views
    const pageViewIds = [];
    const viewToPageMap = {};

    for (const pageItem of pages) {
        for (const view of pageItem.views) {
            pageViewIds.push(view.id);
            viewToPageMap[view.id] = pageItem.page.id;
        }
    }

    const databaseViewIds = [];
    const viewToDatabaseMap = {};

    for (const db of databases) {
        if (db.views && Array.isArray(db.views)) {
            for (const view of db.views) {
                databaseViewIds.push(view.id);
                viewToDatabaseMap[view.id] = db.database.id;
            }
        }
    }

    const databaseIds = databases.map(db => db.database.id);

    // Search for all document types
    const documentRepository = new DocumentService().getRepository();
    const queryBuilder = documentRepository.createQueryBuilder('documents')
        .leftJoin('Record', 'record', 'documents.recordId = record.id AND documents.databaseId = record.databaseId')
        .where('documents.workspaceId = :workspaceId', { workspaceId })
        .andWhere('(documents.contentText LIKE :query OR documents.name LIKE :query)')
        .setParameter('query', `%${decodedQuery}%`);

    const whereConditions = [];
    const parameters: any = { workspaceId, query: `%${decodedQuery}%`, userId };

    if (pageViewIds.length > 0) {
        whereConditions.push('(documents.viewId IN (:...pageViewIds))');
        parameters.pageViewIds = pageViewIds;
    }

    if (databaseViewIds.length > 0) {
        whereConditions.push('(documents.viewId IN (:...databaseViewIds))');
        parameters.databaseViewIds = databaseViewIds;
    }

    // Personal documents (created by or assigned to user, not associated with any database or record)
    whereConditions.push('((documents.createdById = :userId OR documents.assignedToUserIds LIKE :likeUserId) AND documents.viewId IS NULL AND documents.databaseId IS NULL AND documents.recordId IS NULL)');
    parameters.likeUserId = `%${userId}%`;

    // Documents in records in databases the user has access to (excluding deleted records)
    if (databaseIds.length > 0) {
        whereConditions.push('(documents.databaseId IN (:...databaseIds) AND documents.viewId IS NULL AND record.deletedAt IS NULL)');
        parameters.databaseIds = databaseIds;
    }

    queryBuilder.andWhere(`(${whereConditions.join(' OR ')})`);
    queryBuilder.setParameters(parameters);

    const documents = await queryBuilder.getMany();

    for (const doc of documents) {
        const contentMatch = searchWithContext(doc.contentText || '', decodedQuery);
        const nameMatch = searchWithContext(doc.name, decodedQuery);

         if (contentMatch.match || nameMatch.match) {
            let path = '';

            if (doc.viewId) {
                if (viewToPageMap[doc.viewId]) {
                    path = `/${viewToPageMap[doc.viewId]}/views/${doc.viewId}?id=${doc.id}`;
                } else if (viewToDatabaseMap[doc.viewId]) {
                    path = `/databases/${viewToDatabaseMap[doc.viewId]}/views/${doc.viewId}?id=${doc.id}`;
                }
            } else if (doc.databaseId && doc.recordId) {
                let tabParam = '';

                const docName = (doc.name || '').toLowerCase();
                if (docName.includes('note') ||
                    (!doc.viewId && doc.databaseId && doc.recordId)) {
                    tabParam = 'notes';
                    // Include the document ID to open the specific note
                    path = `/databases/${doc.databaseId}/records/${doc.recordId}?tab=${tabParam}&noteId=${doc.id}`;
                }
                // else if (docName.includes('summary')) {
                //     tabParam = 'summary';
                // }
                // else if (docName.includes('activity')) {
                //     tabParam = 'activity';
                // }
                else {
                    path = `/databases/${doc.databaseId}/records/${doc.recordId}${tabParam ? `?tab=${tabParam}` : ''}`;
                }
            }


            if (path) {
                results.push({
                    id: doc.id,
                    title: doc.name || 'Untitled',
                    content: contentMatch.preview,
                    path: path,
                    publishedAt: doc.createdAt,
                    source: {
                        pageId: viewToPageMap[doc.viewId],
                        viewId: doc.viewId,
                        documentId: doc.id,
                        databaseId: doc.databaseId,
                        recordId: doc.recordId,
                    },
                    viewType: ViewType.Document,
                    highlight: contentMatch.match || nameMatch.match,
                });
            }
        }
    }


    for (const user of users) {
        const nameMatch = searchWithContext(user.title, decodedQuery);
        if (nameMatch.match) {
            results.push({
                id: user.id,
                name: user.title,
                content: "Workspace member",
                path: `/settings/members`,
                image: typeof user.image === 'string' ? user.image : user.image?.url,
                highlight: nameMatch.match
            });
        }
    }

    // Search for reminders
    const reminderService = new ReminderService();
    const reminderQueryBuilder = reminderService.getRepository().createQueryBuilder('reminder')
        .leftJoin('Record', 'reminderRecord', 'reminder.recordId = reminderRecord.id AND reminder.databaseId = reminderRecord.databaseId')
        .where('reminder.workspaceId = :workspaceId', { workspaceId });

    reminderQueryBuilder.andWhere('(reminder.title LIKE :query OR reminder.description LIKE :query)')
        .setParameter('query', `%${decodedQuery}%`);

    const reminderWhereConditions = [];
    const reminderParameters: any = { workspaceId, query: `%${decodedQuery}%`, userId };

    // Personal reminders (created by or assigned to user, not associated with any database or record)
    reminderWhereConditions.push('((reminder.createdById = :userId OR reminder.assignedToUserIds LIKE :likeUserId) AND reminder.databaseId IS NULL AND reminder.recordId IS NULL)');
    reminderParameters.likeUserId = `%${userId}%`;

    // Reminders in records in databases the user has access to (excluding deleted records)
    if (databaseIds.length > 0) {
        reminderWhereConditions.push('(reminder.databaseId IN (:...databaseIds) AND reminderRecord.deletedAt IS NULL)');
        reminderParameters.databaseIds = databaseIds;
    }

    reminderQueryBuilder.andWhere(`(${reminderWhereConditions.join(' OR ')})`);
    reminderQueryBuilder.setParameters(reminderParameters);

    const reminders = await reminderQueryBuilder.getMany();

    for (const reminder of reminders) {
        const titleMatch = searchWithContext(reminder.title || '', decodedQuery);
        const descriptionMatch = searchWithContext(reminder.description || '', decodedQuery);

        if (titleMatch.match || descriptionMatch.match) {
            let path = '';

            if (reminder.databaseId && reminder.recordId) {
                // Include the reminder ID to open the specific reminder
                path = `/databases/${reminder.databaseId}/records/${reminder.recordId}?tab=reminders&reminderId=${reminder.id}`;
            } else {
                path = `/reminders?reminderId=${reminder.id}`;
            }

            results.push({
                id: reminder.id,
                title: reminder.title || 'Untitled Reminder',
                content: descriptionMatch.preview || reminder.description?.substring(0, 100) + (reminder.description?.length > 100 ? '...' : '') || '',
                path: path,
                publishedAt: reminder.createdAt,
                source: {
                    databaseId: reminder.databaseId,
                    recordId: reminder.recordId,
                },
                highlight: titleMatch.match || descriptionMatch.match,
            });
        }
    }

    const uniqueResults = results.filter((result, index, self) =>
        index === self.findIndex((r) => r.id === result.id)
      );

      const startIndex = (page - 1) * pageSize;
      const endIndex = startIndex + pageSize;
      const paginatedResults = uniqueResults.slice(startIndex, endIndex);

    return {
        results: paginatedResults,
        pagination: {
            page,
            pageSize,
            totalItems: uniqueResults.length,
            totalPages: Math.ceil(uniqueResults.length / pageSize),
        },
    };
};

export interface CompleteWorkspaceSetupData extends Pick<Workspace, "orgPhoneNumber" | 'orgSize' | 'orgType' | 'orgUseCase'> {
}

export const CompleteWorkspaceSetup = async (userId: string, workspaceId: string, data: CompleteWorkspaceSetupData) => {
    const orgUseCase = (data.orgUseCase || '').trim()
    const orgPhoneNumber = (data.orgPhoneNumber || '').trim()
    const orgSize = (data.orgSize || '').trim()
    const orgType = (data.orgType || '').trim()
    if (!orgUseCase) {
        throw new RequiredParameterError("orgUseCase")
    }
    if (!orgPhoneNumber) {
        throw new RequiredParameterError("orgPhoneNumber")
    }
    if (!orgSize) {
        throw new RequiredParameterError("orgSize")
    }
    if (!orgType) {
        throw new RequiredParameterError("orgType")
    }
    const member = await GetMyWorkspace(userId, workspaceId)
    if (!HasPermission(member, WorkspacePermission.UpdateWorkspace)) {
        throw new UnauthorizedError(ErrorMessage.UnableToAuthorize)
    }
    if (member.workspace.isSetupCompleted) {
        throw new BadRequestError(ErrorMessage.UnableToProcessRequest)
    }
    await SetupInternalWorkspaceDatabases(workspaceId, userId)
    const update: Partial<Workspace> = {
        orgUseCase, orgPhoneNumber, orgSize, orgType, isSetupCompleted: true
    }
    const workspaceService = new WorkspaceService();
    await workspaceService.update({id: workspaceId}, update)
    member.workspace = {...member.workspace, ...update}

    await ProvisionOpendashboardSiteSender(member.workspace.domain)

    RecalculateWorkspaceStats(workspaceId).then()
    const {billingCycle} = await startBillingCycle(workspaceId, undefined, undefined, undefined, undefined, undefined, true)

    return {member, billingCycle}
}

export interface CreateWorkspaceRequestData {
    name: string,
    domain?: string
}

export const CreateWorkspace = async (user: User, data: CreateWorkspaceRequestData): Promise<MyWorkspace> => {
    const name = data.name
    let domain = data.domain || ''

    const userId = user.id

    const workspaceService = new WorkspaceService();
    const workspaceMemberService = new WorkspaceMemberService();

    if (!name) {
        throw new RequiredParameterError("name")
    }
    if (domain.toLowerCase() === 'all' || name.toLowerCase() === 'all') {
        throw new InvalidParameterError(ErrorMessage.AllIsReservedCannotBeWorkspaceNameOrDomain)
    }
    if (name.toLowerCase().includes('opendashboard') || domain.toLowerCase().includes('opendashboard')) {
        throw new InvalidParameterError(ErrorMessage.NameCannotContainOpendashboard)
    }

    if (!domain) {
        const domains = await workspaceService.generateUniqueDomain(name)
        if (domains.length === 0) {
            throw new ServerProcessingError(ErrorMessage.UnableToPerformAction)
        }
        domain = domains[0]
    }
    const workspaceData: CreateWorkspaceData = {
        name: name,
        createdById: userId,
        domain: domain,
        ownerId: userId
    }
    const workspace = await workspaceService.createWorkspace(workspaceData);

    const workspaceMemberData: CreateWorkspaceMember = {
        invitedById: userId,
        workspaceId: workspace.id,
        userId: userId,
        role: WorkspaceMemberRole.Owner,
    }
    const workspaceMember = await workspaceMemberService.createWorkspaceMember(workspaceMemberData)

    await TriggerNewWorkspaceWorkflow(user, workspace)

    return {
        workspace, workspaceMember, membersCount: 1, planId: '', priceId: '', billingCycle: null
    }
}

export interface OnboardingDetails {
    myselfOrCompany?: string
    orgUseCase?: string
    orgType?: string
    orgSize?: string
    orgRole?: string
    orgPhoneNumber?: string
    getStartedFrom?: string
    name?: string
    domain?: string
    logo?: string
    teamEmails?: string
    gettingStarted_Templates?: string[]
    gettingStarted_Code?: string
    referralCode?: string
}

export const CreateWorkspaceViaOnboarding = async (user: User, data: OnboardingDetails) => {
    const name = data.name
    let domain = data.domain

    const userId = user.id

    const workspaceService = new WorkspaceService();
    const workspaceMemberService = new WorkspaceMemberService();

    if (!name) {
        throw new RequiredParameterError("name")
    }

    if (!domain) {
        const domains = await workspaceService.generateUniqueDomain(name)
        if (domains.length === 0) {
            throw new ServerProcessingError(ErrorMessage.UnableToPerformAction)
        }
        domain = domains[0]
    }
    if (domain.toLowerCase() === 'all' || name.toLowerCase() === 'all') {
        throw new InvalidParameterError(ErrorMessage.AllIsReservedCannotBeWorkspaceNameOrDomain)
    }
    if (name.toLowerCase().includes('opendashboard') || domain.toLowerCase().includes('opendashboard')) {
        throw new InvalidParameterError(ErrorMessage.NameCannotContainOpendashboard)
    }
    const wspWithDomain = await workspaceService.findOne({domain})
    if (wspWithDomain) {
        const domains = await workspaceService.generateUniqueDomain(name, 50)
        if (domains.length === 0) {
            throw new ServerProcessingError(ErrorMessage.UnableToPerformAction)
        }
        throw new BadRequestError(`Workspace with domain "${domain}" already exists. Alternative domains are ${domains.slice(0, 5).join(', ')}`)
    }
    let templatesIdsToInstall: string[] = []

    if (data.getStartedFrom === 'from_templates') {
        const templates = data.gettingStarted_Templates || []
        if (templates.length === 0) {
            throw new RequiredParameterError("gettingStarted_Templates")
        }

        for (let template of templates) {
            if (TemplateOnboardingMapping[template]) {
                templatesIdsToInstall.push(TemplateOnboardingMapping[template])
            }
        }
    } else if (data.getStartedFrom === 'from_code') {
        // fetch the purchase from the code, add to templatesIdsToInstall
        const purchase = await new TemplatePurchaseService().findOne({purchasedById: userId, id: data.gettingStarted_Code, workspaceLicense: TemplatePurchaseWorkspaceLicense.Multi})
        if (!purchase) {
            throw new NotfoundError(ErrorMessage.PurchaseNotFoundForTemplate)
        }
        templatesIdsToInstall.push(purchase.templateId)
    }
    let emailsToInvite: string[] = []
    if (data.teamEmails) {
        // split teamEmails by comma, space, linebreak or whitespace, add to emailsToInvite
        emailsToInvite = (data.teamEmails || '').split(/[\s,]+/).filter(email => {
            if (!validateEmail(email)) {
                throw new InvalidParameterError(`'${email}' is not a valid email address`);
            }
            return email.toLocaleLowerCase();
        });
    }

    const orgUseCase = (data.orgUseCase || '').trim()
    const orgPhoneNumber = (data.orgPhoneNumber || '').trim()
    const orgSize = (data.orgSize || '').trim()
    const orgType = (data.orgType || '').trim()

    let affiliateId: string = null
    if (data.referralCode) {
        try {
            const {affiliate} = await GetAffiliateByReferralCode(data.referralCode)
            if (affiliate.userId !== userId) affiliateId = affiliate.id
        } catch (e) {
        }
    }

    const workspaceData: CreateWorkspaceData = {
        name: name,
        createdById: userId,
        domain: domain,
        ownerId: userId,
        logo: data.logo,
        orgPhoneNumber,
        orgSize,
        orgType,
        orgUseCase,
        isSetupCompleted: true,
        meta: {
            onboardingInfo: data
        },
        affiliateId
    }

    // create workspace and workspace member - done
    // install the contacts and companies database - done
    // create getting started guide - done
    // install templatesIdsToInstall - done,
    // invite people in emailsToInvite - done
    // mark workspace as setup completed - done

    // return and redirect to the getting started page

    const queryRunner = workspaceService.getRepository().queryRunner || dbDataSource.createQueryRunner();
    const entityManager = queryRunner.manager
    const hasExistingTransaction = entityManager.queryRunner.isTransactionActive

    if (!hasExistingTransaction) {
        await entityManager.queryRunner.startTransaction()
    }
    try {
        const workspace = await workspaceService.createWorkspace(workspaceData);
        const workspaceId = workspace.id

        const workspaceMemberData: CreateWorkspaceMember = {
            invitedById: userId,
            workspaceId: workspace.id,
            userId: userId,
            role: WorkspaceMemberRole.Owner,
        }
        const workspaceMember = await workspaceMemberService.createWorkspaceMember(workspaceMemberData)

        // const {companiesDatabase, contactsDatabase} = await SetupInternalWorkspaceDatabases(workspaceId, userId)
        const {gettingStartedPage} = await SetupGettingStartedPage(workspaceId, userId)

        templatesIdsToInstall = arrayDeDuplicate(templatesIdsToInstall)
        for (const id of templatesIdsToInstall) {
            await PurchaseTemplate(userId, id, {
                workspaceId,
                workspaceLicense: TemplatePurchaseWorkspaceLicense.Multi,
                currency: PaymentCurrency.USD
            })
            await InstallTemplate(userId,
                {
                    workspaceId,
                    prepareData: {installAll: true, pages: {}, databases: {}, workflows: {}},
                    templateId: id
                },
                {contactsDbId: '', companiesDbId: ''}
            )
        }

        emailsToInvite = arrayDeDuplicate(emailsToInvite)
        if (emailsToInvite.length > 0) await InviteWorkspaceMembers(userId, workspaceId, {emails: emailsToInvite, role: WorkspaceMemberRole.Member}, false)

        RecalculateWorkspaceStats(workspaceId).then()
        const {billingCycle} = await startBillingCycle(workspaceId, undefined, undefined, undefined, undefined, undefined, true)

        if (!hasExistingTransaction && entityManager.queryRunner.isTransactionActive) {
            await entityManager.queryRunner.commitTransaction()
        }
        await TriggerNewWorkspaceWorkflow(user, workspace)

        await ProvisionOpendashboardSiteSender(workspace.domain)

        const myWorkspace: MyWorkspace = {
            workspace, workspaceMember, membersCount: 1, planId: '', priceId: '', billingCycle: billingCycle
        }
        return {
            myWorkspace, gettingStartedPage
        }
    } catch (e) {
        if (!hasExistingTransaction && entityManager.queryRunner.isTransactionActive) {
            await entityManager.queryRunner.rollbackTransaction()
        }
        throw e
    }

}

export interface UpdateWorkspaceData {
    name: string
    timezone: string
}

export const UpdateWorkspace = async (userId: string, workspaceId: string, data: UpdateWorkspaceData): Promise<MyWorkspace> => {
    const name = (data.name || '').trim()
    const timezone = data.timezone || "UTC"
    if (!name) {
        throw new RequiredParameterError("name")
    }
    const member = await GetMyWorkspace(userId, workspaceId)
    if (!HasPermission(member, WorkspacePermission.UpdateWorkspace)) {
        throw new UnauthorizedError(ErrorMessage.UnableToAuthorize)
    }
    const update = {
        name, timezone
    }
    const workspaceService = new WorkspaceService();
    await workspaceService.update({id: workspaceId}, update)
    member.workspace = {...member.workspace, ...update}

    return member
}

export const UpdateWorkspaceLogo = async (userId: string, workspaceId: string, request: Request) => {
    const member = await GetMyWorkspace(userId, workspaceId)
    if (!HasPermission(member, WorkspacePermission.UpdateWorkspace)) {
        throw new UnauthorizedError(ErrorMessage.UnableToAuthorize)
    }
    const date = new Date(member.workspace.createdAt)
    const month = date.getMonth() + 1
    const year = date.getFullYear()
    const day = date.getDate()

    const result = await ProcessFormToSpaceUpload(request, `workspaces/${year}/${month}/${day}/${userId}`, 'photo.png', FileType.Image)

    const update: Partial<Workspace> = {
        logo: `${result.location}?t=${new Date().getTime()}`
    }
    const service = new WorkspaceService();
    await service.update({id: workspaceId}, update)

    member.workspace = {...member.workspace, ...update}

    return member
}

export interface CreateWorkspaceInvitationData extends Pick<CreateWorkspaceInvitation, 'email' | 'role'> {
}

export const InviteWorkspaceMember = async (userId: string, workspaceId: string, data: CreateWorkspaceInvitationData) => {
    const email = (data.email || '').trim();
    const role = ((data.role || '').trim() || WorkspaceMemberRole.Member) as WorkspaceMemberRole;

    const {invitations} = await InviteWorkspaceMembers(userId, workspaceId, {emails: [email], role}, true)

    return {
        myInvitation: invitations[0], messageId: invitations[0]?.messageId
    }
}


export const InviteWorkspaceMembers = async (userId: string, workspaceId: string, data: {
    emails: string[],
    role: WorkspaceMemberRole
}, recalculateStats = true) => {
    const emails = data.emails || []
    const role = (data.role || '').trim() || WorkspaceMemberRole.Member;

    if (emails.length === 0) {
        throw new RequiredParameterError("emails");
    }
    if (!role) {
        throw new RequiredParameterError("role");
    }
    if (![WorkspaceMemberRole.Admin, WorkspaceMemberRole.Member, WorkspaceMemberRole.Collaborator].includes(role as WorkspaceMemberRole)) {
        throw new InvalidParameterError("Role is invalid");
    }

    const member = await GetMyWorkspace(userId, workspaceId)
    if (!HasPermission(member, WorkspacePermission.UpdateWorkspace)) {
        throw new UnauthorizedError(ErrorMessage.UnableToAuthorize)
    }
    const userService = new UserService();
    const users = await userService.find({
        email: In(emails)
    })
    const registeredEmails = users.map(u => u.email)
    const unregistered = arrayDiff(emails, registeredEmails)
    // create the users that don't exist
    const toCreate: CreateUser[] = []
    for (const email of unregistered) {
        const uData: CreateUser = {
            firstName: "",
            lastName: "",
            email: email,
        }
        toCreate.push(uData)
    }
    const createdUsers = await userService.batchCreate(toCreate)

    users.push(...createdUsers)

    const usersMap: { [id: string]: User } = {}
    for (const user of users) {
        usersMap[user.id] = user
    }
    const memberService = new WorkspaceMemberService()
    const members = await memberService.find({workspaceId})

    // for users that are not members of the workspace, send them the invite
    const userIds: string[] = Object.keys(usersMap)
    const nonMemberIds: string[] = arrayDiff(userIds, members.map(m => m.userId))

    const service = new WorkspaceInvitationService()

    const inviteDatas: CreateWorkspaceInvitation [] = []
    for (let id of nonMemberIds) {
        const user = usersMap[id]
        const token = generateToken()
        const expiresAt = datePlusMonths(new Date(), 1)

        const inviteData: CreateWorkspaceInvitation = {
            email: user.email,
            invitedById: userId,
            workspaceId,
            userId: user?.id || null,
            role: role as WorkspaceMemberRole,
            token: token,
            expiresAt: expiresAt,
        }
        inviteDatas.push(inviteData)
    }
    const invites = await service.batchAdd(inviteDatas)

    const invitations: MyWorkspaceInvitation[] = []
    for (let invite of invites) {
        const myInvitation: MyWorkspaceInvitation = {
            invitation: invite,
            workspace: member.workspace,
        }
        myInvitation.messageId = await sendWorkspaceInvitationEmail(myInvitation)
        invitations.push(myInvitation)
    }
    if (recalculateStats) RecalculateWorkspaceStats(workspaceId).then()
    return {invitations}
}

const sendWorkspaceInvitationEmail = async (invitation: MyWorkspaceInvitation) => {
    const inviteLink = appUrl(`/invitation?token=${encodeURIComponent(invitation.invitation.token)}&workspaceId=${encodeURIComponent(invitation.workspace.id)}`);

    const to: EmailUser = {
        email: invitation.invitation.email,
        name: ''
    }
    const subject = `Invitation to join ${invitation.workspace.name} on Opendashboard`
    const message = `
	Hello there, <br/> You were invited to join <a href="${inviteLink}"><strong>${invitation.workspace.name}</strong></a> workspace in Opendashboard.<br/>
	<p>This link expires in 30 days</p>
	`
    const button = {
        label: 'View Invitation →',
        url: inviteLink
    }
    const messageId = await SendEmailWithContent(to, subject, message, button)

    return messageId
}

export const ResendWorkspaceInvitation = async (userId: string, workspaceId: string, inviteId: number) => {
    const member = await GetMyWorkspace(userId, workspaceId)
    if (!HasPermission(member, WorkspacePermission.UpdateWorkspace)) {
        throw new UnauthorizedError(ErrorMessage.UnableToAuthorize)
    }

    const service = new WorkspaceInvitationService()
    const invitations = await service.find({
        id: inviteId, workspaceId, expiresAt: MoreThanOrEqual(new Date())
    })

    const invitation = invitations[0]
    if (!invitation) {
        throw new NotfoundError(ErrorMessage.EntityNotFound)
    }
    const myInvitation: MyWorkspaceInvitation = {
        invitation,
        workspace: member.workspace
    }
    const messageId = await sendWorkspaceInvitationEmail(myInvitation)

    return {
        myInvitation, messageId
    }
}

export const GetAllWorkspaceInvitations = async (userId: string, workspaceId: string): Promise<WorkspaceInvitation[]> => {
    const member = await GetMyWorkspace(userId, workspaceId)
    if (!HasPermission(member, WorkspacePermission.ReadWorkspace)) {
        throw new UnauthorizedError(ErrorMessage.UnableToAuthorize)
    }
    const service = new WorkspaceInvitationService()

    return await service.find({
        workspaceId, expiresAt: MoreThanOrEqual(new Date())
    })
}

export const CancelWorkspaceInvitation = async (userId: string, workspaceId: string, inviteId: number) => {
    const member = await GetMyWorkspace(userId, workspaceId)
    if (!await HasPermission(member, WorkspacePermission.UpdateWorkspace)) {
        throw new UnauthorizedError(ErrorMessage.UnableToAuthorize)
    }

    const service = new WorkspaceInvitationService()

    const invitations = await service.find({id: inviteId, workspaceId, expiresAt: MoreThanOrEqual(new Date())})
    const invitation = invitations[0]
    if (!invitation) {
        throw new NotfoundError(ErrorMessage.EntityNotFound)
    }
    await service.remove({id: invitation.id})

    return true
}

export const GetWorkspaceInvitation = async (userId: string, workspaceId: string, token: string): Promise<MyWorkspaceInvitation> => {
    if (!token) {
        throw new RequiredParameterError("token");
    }
    const user = await GetProfile(userId)

    const service = new WorkspaceInvitationService()
    const invitation = await service.findInvitation(workspaceId, userId, user.email, token);
    if (!invitation) {
        throw new NotfoundError(ErrorMessage.EntityNotFound)
    }
    const workspaceService = new WorkspaceService();
    const workspace = await workspaceService.findById(workspaceId)

    return {
        workspace, invitation
    }
}

export const AcceptWorkspaceInvitation = async (userId: string, workspaceId: string, token: string): Promise<MyWorkspace> => {
    if (!token) {
        throw new RequiredParameterError("token");
    }
    const user = await GetProfile(userId)

    const service = new WorkspaceInvitationService()
    const invitation = await service.findInvitation(workspaceId, userId, user.email, token);
    if (!invitation) {
        throw new NotfoundError(ErrorMessage.EntityNotFound)
    }
    const workspaceService = new WorkspaceService();
    const workspace = await workspaceService.findById(workspaceId)

    const workspaceMemberService = new WorkspaceMemberService()
    const workspaceMember = await workspaceMemberService.createWorkspaceMember({
        role: invitation.role,
        workspaceId: workspaceId,
        userId: userId,
        invitedById: invitation.invitedById,
    })

    await service.remove({id: invitation.id})
    // await clearWorkspaceMemberListCache(workspaceId)

    await broadcastNewWorkspaceMembers(workspaceId, [userId])

    RecalculateWorkspaceStats(workspaceId).then()
    return {
        workspace, workspaceMember, membersCount: 1, planId: '', priceId: '', billingCycle: null // Doesn't matter as authContext in frontend will reload it.
    }
}

export const DeclineWorkspaceInvitation = async (userId: string, workspaceId: string, token: string) => {
    if (!workspaceId) {
        throw new RequiredParameterError("email");
    }
    if (!token) {
        throw new RequiredParameterError("role");
    }
    const user = await GetProfile(userId)

    const service = new WorkspaceInvitationService()
    const invitation = await service.findInvitation(workspaceId, userId, user.email, token);
    if (!invitation) {
        throw new NotfoundError(ErrorMessage.EntityNotFound)
    }

    await service.remove({id: invitation.id})

    return true
}

export const addCollaboratorsToWorkspace = async (invitedById: string, userIds: string[], workspaceId: string, billingCycle: BillingCycle) => {
    const memberService = new WorkspaceMemberService();
    const members = await memberService.find({ workspaceId });

    const nonMemberIds: string[] = arrayDiff(userIds, members.map(m => m.userId));

    let collaboratorsCount = 0;
    for (const member of members) {
        if (member.role === WorkspaceMemberRole.Collaborator) {
            collaboratorsCount++;
        }
    }

    collaboratorsCount += nonMemberIds.length;

    if (collaboratorsCount > billingCycle.collaboratorsQuota) {
        throw new BadRequestError(ErrorMessage.MaximumCollaboratorLimitReached);
    }

    const toAdd: CreateWorkspaceMember[] = [];
    for (const id of nonMemberIds) {
        toAdd.push({
            workspaceId,
            userId: id,
            role: WorkspaceMemberRole.Collaborator,
            invitedById
        });
    }

    if (toAdd.length > 0) {
        const addedMembers = await memberService.batchAdd(toAdd);
        return addedMembers;
    }

    RecalculateWorkspaceStats(workspaceId).then()

    return [];
}
export const GetWorkspaceMembers = async (userId: string, workspaceId: string): Promise<MyWorkspaceMember[]> => {
    const member = await GetMyWorkspace(userId, workspaceId)
    if (!HasPermission(member, WorkspacePermission.ReadWorkspace)) {
        throw new UnauthorizedError(ErrorMessage.UnableToAuthorize)
    }
    return getWorkspaceMemberNoCacheNoAuth(workspaceId)
    // return (await getCachedWorkspaceMemberList(workspaceId)).members
}

export const broadcastNewWorkspaceMembers = async (workspaceId: string, userIds: string[]) => {
    const fullMembers = await getWorkspaceMemberNoCacheNoAuth(workspaceId)

    const members = fullMembers.filter(m => userIds.includes(m.user.id))

    broadcastWorkspaceMembersAdded(workspaceId, members)
}

export interface UpdateWorkspaceMemberData extends Pick<CreateWorkspaceInvitation, 'userId' | 'role'> {
}

export const UpdateWorkspaceMember = async (userId: string, workspaceId: string, data: UpdateWorkspaceMemberData) => {
    const role = data.role
    const memberId = data.userId || ''
    if (![WorkspaceMemberRole.Admin, WorkspaceMemberRole.Member, WorkspaceMemberRole.Collaborator].includes(role as WorkspaceMemberRole)) {
        throw new InvalidParameterError("Role is invalid");
    }
    const member = await GetMyWorkspace(userId, workspaceId)
    if (!HasPermission(member, WorkspacePermission.UpdateWorkspace)) {
        throw new UnauthorizedError(ErrorMessage.UnableToAuthorize)
    }
    const service = new WorkspaceMemberService()
    const memberToUpdate = await service.findOne({
        workspaceId, userId: memberId
    })
    if (!memberToUpdate) {
        throw new NotfoundError(ErrorMessage.EntityNotFound)
    }
    if (memberToUpdate.role === WorkspaceMemberRole.Owner) {
        throw new BadRequestError(ErrorMessage.UnableToAuthorize)
    }
    await service.update({workspaceId, userId: memberId}, {role})
    await RestoreWorkspaceLimitedFunctionality(member.workspace)
    return true
}


export interface DeleteWorkspaceMemberData extends Pick<CreateWorkspaceInvitation, 'userId'> {
}

export const DeleteWorkspaceMember = async (userId: string, workspaceId: string, data: DeleteWorkspaceMemberData) => {
    const memberId = data.userId || ''
    const member = await GetMyWorkspace(userId, workspaceId)
    if (!HasPermission(member, WorkspacePermission.UpdateWorkspace)) {
        throw new UnauthorizedError(ErrorMessage.UnableToAuthorize)
    }
    const service = new WorkspaceMemberService()
    const memberToUpdate = await service.findOne({
        workspaceId, userId: memberId
    })
    if (!memberToUpdate) {
        throw new NotfoundError(ErrorMessage.EntityNotFound)
    }
    if (memberToUpdate.role === WorkspaceMemberRole.Owner) {
        throw new BadRequestError(ErrorMessage.UnableToAuthorize)
    }
    await service.hardRemove({workspaceId, userId: memberId})
    // RecalculateWorkspaceStats(workspaceId).then()
    await RestoreWorkspaceLimitedFunctionality(member.workspace)
    return true
}

export const MakeWorkspaceMemberOwner = async (userId: string, workspaceId: string, data: DeleteWorkspaceMemberData) => {
    const memberId = data.userId || ''
    const member = await GetMyWorkspace(userId, workspaceId)
    if (!HasPermission(member, WorkspacePermission.DeleteWorkspace)) {
        throw new UnauthorizedError(ErrorMessage.UnableToAuthorize)
    }
    const service = new WorkspaceMemberService()
    const memberToUpdate = await service.findOne({
        workspaceId, userId: memberId
    })
    if (!memberToUpdate) {
        throw new NotfoundError(ErrorMessage.EntityNotFound)
    }
    if (memberToUpdate.role === WorkspaceMemberRole.Owner) {
        throw new BadRequestError(ErrorMessage.UnableToAuthorize)
    }
    await service.update({workspaceId, role: WorkspaceMemberRole.Owner}, {role: WorkspaceMemberRole.Admin})
    await service.update({workspaceId, userId: memberId}, {role: WorkspaceMemberRole.Owner})
    return true
}

export const ToggleSupportAccess = async (userId: string, workspaceId: string, enable: boolean) => {
    const member = await GetMyWorkspace(userId, workspaceId)
    if (!HasPermission(member, WorkspacePermission.UpdateWorkspace)) {
        throw new UnauthorizedError(ErrorMessage.UnableToAuthorize)
    }
    return ToggleWorkspaceSupportAccessNoAuth(userId, workspaceId, enable)
}

export const ToggleWorkspaceSupportAccessNoAuth = async (userId: string, workspaceId: string, enable: boolean) => {
    if (enable) {
        const userService = new UserService()
        const supportUser = await userService.findOne({isSupportAccount: true})
        if (!supportUser) throw new NotfoundError(ErrorMessage.AssocEntityNotFound)

        const memberService = new WorkspaceMemberService()
        try {
            await memberService.createWorkspaceMember({
                userId: supportUser.id,
                workspaceId,
                role: WorkspaceMemberRole.SupportUser,
                invitedById: userId
            })
        } catch (e) {
            if (e ! instanceof UniqueConstraintError) {
                throw e
            }
        }
        const workspaceService = new WorkspaceService()
        await workspaceService.update({id: workspaceId}, {isSupportAccessEnabled: true})
    } else {
        const workspaceService = new WorkspaceService()
        await workspaceService.update({id: workspaceId}, {isSupportAccessEnabled: false})

        const memberService = new WorkspaceMemberService()
        await memberService.hardRemove({
            workspaceId,
            role: WorkspaceMemberRole.SupportUser,
        })

    }
    return true
}


export const TurnOffAllSupportAccess = async (all = false, expiryDate = 3) => {
    const workspaceService = new WorkspaceService()
    const memberService = new WorkspaceMemberService()

    if (all) {
        await memberService.hardRemove({role: WorkspaceMemberRole.SupportUser})
        await workspaceService.update({isSupportAccessEnabled: true}, {isSupportAccessEnabled: false})
    } else {
        const raw = await memberService.find({role: WorkspaceMemberRole.SupportUser, createdAt: LessThan(datePlusMonths(new Date(), expiryDate))})
        const ids = raw.map(w => w.workspaceId)

        await memberService.hardRemove({role: WorkspaceMemberRole.SupportUser, workspaceId: In(ids)})
        await workspaceService.update({id: In(ids)}, {isSupportAccessEnabled: false})
    }
    return true
}

// const getWorkspaceMemberList = async (workspaceId: string) => (await getCachedWorkspaceMemberList(workspaceId)).members

export const getWorkspaceMemberNoCacheNoAuth = async (workspaceId: string) => {
    const service = new WorkspaceMemberService();

    const repo = service.getRepository()
    const qBQuery = repo.createQueryBuilder("wM")
        .addSelect("u")
        .leftJoin(User, "u", "wM.userId=u.id")
        .where("wM.workspaceId=:workspaceId ANd wM.role != :supportUser")
        .setParameters({
            workspaceId,
            supportUser: WorkspaceMemberRole.SupportUser
        })

    const rawResults = await qBQuery.getRawMany()

    return rawResults.map(r => {
        const result: MyWorkspaceMember = {
            user: {} as User,
            workspaceMember: {} as WorkspaceMember
        }
        for (let key of Object.keys(r)) {
            const [pre, field] = key.split("_")
            if (pre === "u") {
                result.user[field] = r[key]
            } else if (pre === "wM") {
                result.workspaceMember[field] = r[key]
            }
        }

        return result
    })
}

export const getWorkspaceMembersAsPersons = async (workspaceId: string) => {
    const members = await getWorkspaceMemberNoCacheNoAuth(workspaceId)
    return members.map(m => {
        const p: Person = {
            firstName: m.user.firstName, id: m.user.id,
            image: {type: ObjectType.Image, url: m.user.profilePhoto},
            lastName: m.user.lastName,
            title: `${m.user.firstName} ${m.user.lastName}`.trim(),
            email: m.user.email
        }

        return p
    })
}

// const getCachedWorkspaceMemberList = async (workspaceId: string, cacheExp = 30) => {
//     const key = redisKey('workspace-members', workspaceId)
//     let result = {
//         members: [] as MyWorkspaceMember[],
//         cacheExp,
//         isCache: false,
//     }
//
//     const cache = await redisGet(key)
//     if (cache) {
//         const parsed = JSON.parse(cache)
//         if (parsed) {
//             result.members = parsed.members
//             result.isCache = true
//             return result
//         }
//     }
//     const members = await getWorkspaceMemberNoCacheNoAuth(workspaceId)
//
//     const cacheData = {
//         members,
//     }
//     result.members = members
//
//     await redisSet(key, JSON.stringify(cacheData), cacheExp)
//
//     return result
// }

// const clearWorkspaceMemberListCache = async (workspaceId: string) => {
//     const key = redisKey('workspace-members', workspaceId)
//     await redisDelete(key)
// }

export interface DeleteWorkspaceRequestData {
    reason: string
}

export const DeleteWorkspace = async (userId: string, workspaceId: string, data: DeleteWorkspaceRequestData) => {
    const reason = data.reason ? data.reason.trim() : ''
    if (!reason) {
        throw new RequiredParameterError("reason")
    }
    const member = await GetMyWorkspace(userId, workspaceId)
    if (!HasPermission(member, WorkspacePermission.DeleteWorkspace)) {
        throw new UnauthorizedError(ErrorMessage.UnableToAuthorize)
    }
    const service = new WorkspaceService()
    await service.update({id: workspaceId}, {deletedAt: new Date(), deletionReason: reason})

    return true
}

export const SwitchToWorkspace = async (userId: string, workspaceId: string) => {
    const member = await GetMyWorkspace(userId, workspaceId)
    if (!HasPermission(member, WorkspacePermission.ReadWorkspace)) {
        throw new UnauthorizedError(ErrorMessage.UnableToAuthorize)
    }
    const service = new UserService()
    await service.update({id: userId}, {activeWorkspaceId: workspaceId})

    return true
}
export const getWorkspaceActiveRiskLog = async (userId: string, workspaceId: string) => {
    const member = await GetMyWorkspace(userId, workspaceId)
    if (!HasPermission(member, WorkspacePermission.ReadWorkspace)) {
        throw new UnauthorizedError(ErrorMessage.UnableToAuthorize)
    }
    const s = new WorkspaceRiskLogService()
    const riskLog = await s.find({workspaceId})
    return riskLog
}

export interface CreateSenderEmailData extends Pick<WorkspaceSenderEmail, 'email' | 'name'> {
}

export const AddWorkspaceSenderEmail = async (userId: string, workspaceId: string, data: CreateSenderEmailData) => {
    const member = await GetMyWorkspace(userId, workspaceId)
    if (!HasPermission(member, WorkspacePermission.UpdateWorkspace)) {
        throw new UnauthorizedError(ErrorMessage.UnableToAuthorize)
    }
    const email = (data.email || '').trim()
    const name = (data.name || '').trim()

    if (!name) {
        throw new RequiredParameterError("name")
    }
    if (!validateEmail(email)) {
        throw new InvalidParameterError(`'${email}' is not a valid email address`)
    }
    if (!isLocal() && email.includes('opendashboard.site')) {
        throw new InvalidParameterError(ErrorMessage.DomainCannotContainOpendashboardSite)
    }
    const domainUrl = extractDomainFromEmail(email)
    const wDS = new WorkspaceDomainService()
    const s = new WorkspaceSenderService()


    const dS = new DomainService()
    let sGDomain = await dS.findOne({fullAuthorizedDomain: fullAuthorizedDomain(domainUrl)})
    let configs: DomainConfig[] = []
    if (!sGDomain) {
        const res = await SendGrid.addDomain(domainUrl)
        if (!res.isSuccess) {
            throw new ServerProcessingError(res.error)
        }
        const domain = res.data

        configs = []


        configs.push({
            type: DomainConfigType.SPF,
            recordType: RecordType.CNAME,
            host: domain.dns.mail_cname.host,
            value: domain.dns.mail_cname.data
        })
        configs.push({
            type: DomainConfigType.DKIM,
            recordType: RecordType.CNAME,
            host: domain.dns.dkim1.host,
            value: domain.dns.dkim1.data
        })
        configs.push({
            type: DomainConfigType.DKIM,
            recordType: RecordType.CNAME,
            host: domain.dns.dkim2.host,
            value: domain.dns.dkim2.data
        })
        configs.push({
            type: DomainConfigType.DMARC,
            recordType: RecordType.TXT,
            host: `_dmarc.${domainUrl}`,
            value: 'v=DMARC1;p=none;'
        })
        sGDomain = await dS.insert({
            domain: domain.domain,
            subdomain: domain.subdomain,
            fullAuthorizedDomain: `${domain.subdomain}.${domain.domain}`,
            isVerified: false,
            addedByUserId: userId,
            sendgridDomainId: domain.id.toString(),
            configs
        })
    } else {
        configs = sGDomain.configs
    }

    const senders = await s.find({workspaceId})
    const domains = await wDS.find({workspaceId})

    if (senders.length >= member.billingCycle.cyclePlanQuota.senderEmails) {
        throw new BadRequestError(ErrorMessage.MaximumSenderEmailLimitReached)
    }
    let wSPDomain: WorkspaceDomain
    for (const d of domains) {
        if (d.domainId === sGDomain.id) {
            wSPDomain = d
            break
        }
    }
    if (!wSPDomain && domains.length >= member.billingCycle.cyclePlanQuota.senderDomains) {
        throw new BadRequestError(ErrorMessage.MaximumSenderDomainLimitReached)
    }
    if (!wSPDomain) {
        wSPDomain = await wDS.insert({
            workspaceId, domainId: sGDomain.id, addedByUserId: userId, isVerified: sGDomain.isVerified, verifiedAt: sGDomain.isVerified ? new Date() : null
        });
    }
    if (!wSPDomain) {
        throw new BadRequestError(ErrorMessage.UnableToProcessRequest)
    }
    const sender = await (new WorkspaceSenderService().addSender({
        workspaceId, email, name, addedByUserId: userId, workspaceDomainId: String(wSPDomain.id)
    }))
    const verifyResponse = await PromptSenderEmailVerification(sender, member.workspace)

    RecalculateWorkspaceStats(workspaceId).then()

    const domain: DomainWithConfigs = {
        ...wSPDomain, configs, domain: domainUrl
    }

    return {
        sender, domain, verifyResponse
    }
}

export interface SenderWithDomainConfig {
    sender: WorkspaceSenderEmail,
    domain: WorkspaceDomain,
    configs: DomainConfig[]
}

export interface DomainWithConfigs extends WorkspaceDomain {
    domain: string
    configs: DomainConfig[]
}

export const ResendSenderEmailVerification = async (userId: string, workspaceId: string, senderId: number) => {
    const member = await GetMyWorkspace(userId, workspaceId)
    if (!HasPermission(member, WorkspacePermission.UpdateWorkspace)) {
        throw new UnauthorizedError(ErrorMessage.UnableToAuthorize)
    }
    const service = new WorkspaceSenderService()
    const sender = await service.findOne({workspaceId, id: senderId})
    if (!sender) {
        throw new NotfoundError(ErrorMessage.EntityNotFound)
    }
    return PromptSenderEmailVerification(sender, member.workspace)
}

export const PromptSenderEmailVerification = async (sender: WorkspaceSenderEmail, workspace: Workspace) => {
    const service = new TokenService();
    const userId = `${workspace.id}.${sender.id}`
    const token: Token = await service.createToken(userId, TokenType.EmailSenderVerification, {
        value: 2,
        unit: TokenExpiryUnit.Days
    })
    const verifyHash = `${userId}.${encodeURIComponent(token.token)}`;
    const params = [
        // `workspaceId=${workspace.id}`,
        `hash=${verifyHash}`,
        // `senderId=${sender.id}`
    ]
    const emailVerifyLink = apiUrl(`/api/v1/auth/verify-sender?${params.join("&")}`);

    const to: EmailUser = {
        email: sender.email,
        name: `${sender.name}`.trim()
    }
    const subject = `Verify your sender identity for ${workspace.name} - Opendashboard`
    const body = `
    <p style="font-size: 24px; font-weight: 600;">Verify your sender identity for ${workspace.name} </p>
    <p style="color:#313539;">
    We received a request to authorize sending emails from ${sender.email} on behalf of ${workspace.name} in Opendashboard.
    Click on the link below to start using it. Link is valid for 48hours.</p>
    `
    const button = {
        label: 'Verify Email',
        url: emailVerifyLink
    }
    const messageId = await SendEmailWithContent(to, subject, body, button)
    return {messageId, token, verifyHash}
}

export const CompleteSenderEmailVerification = async (encodedHash: string) => {
    const hash = decodeURIComponent(encodedHash)
    const [workspaceId, senderId, tkn] = hash.split('.')
    const userId = `${workspaceId}.${senderId}`

    const service = new TokenService();
    const token: Token = await service.verifyToken(userId, tkn, TokenType.EmailSenderVerification)
    if (!token) {
        throw new BadRequestError(`The token provided is invalid or expired`)
    }
    const senderService = new WorkspaceSenderService()

    await senderService.update({id: Number(senderId)}, {isVerified: true})
    await service.deleteToken(workspaceId, tkn, TokenType.EmailVerification)

    return true
}

export const CompleteDomainVerification = async (userId: string, workspaceId: string, domainId: string) => {
    const member = await GetMyWorkspace(userId, workspaceId)
    if (!HasPermission(member, WorkspacePermission.UpdateWorkspace)) {
        throw new UnauthorizedError(ErrorMessage.UnableToAuthorize)
    }
    const wDS = new WorkspaceDomainService()
    const domain = await wDS.findOne({
        id: domainId, workspaceId
    })
    if (!domain) {
        throw new NotfoundError(ErrorMessage.EntityNotFound)
    }
    if (domain.isVerified) return true

    const dS = new DomainService()
    const sGD = await dS.findOne({id: domain.domainId})
    if (!sGD) throw new NotfoundError(ErrorMessage.EntityNotFound)
    if (!sGD.isVerified) {
        for (const config of sGD.configs) {
            const verified = await verifyDomainRecord(config.host, config.recordType, config.value, config.type !== DomainConfigType.DMARC)
            if (!verified) {
                throw new NotfoundError(ErrorMessage.UnableToVerifyPhrase + ` ${config.recordType} record for ${config.host}`)
            }
        }
        const res = await SendGrid.validateDomain(sGD.sendgridDomainId)
        if (!res.isSuccess) {
            throw new NotfoundError(ErrorMessage.UnableToVerifyDomain)
        }
        await dS.update({id: domain.domainId}, {isVerified: true, verifiedAt: new Date()})
    }

    await wDS.update({domainId: domain.domainId}, {isVerified: true, verifiedAt: new Date()})

    return true
}

export const GetSenderEmails = async (userId: string, workspaceId: string) => {
    const member = await GetMyWorkspace(userId, workspaceId)
    if (!HasPermission(member, WorkspacePermission.ReadWorkspace)) {
        throw new UnauthorizedError(ErrorMessage.UnableToAuthorize)
    }
    const service = new WorkspaceDomainService()
    const senderService = new WorkspaceSenderService()
    const dS = new DomainService()

    const wsDomains = await service.find({workspaceId})
    const senders = await senderService.find({workspaceId})

    const domainIds = arrayDeDuplicate(wsDomains.map(d => d.domainId))
    const usedDomains = await dS.find({id: In(domainIds)})

    const usedMap: { [id: number]: Domain } = {}

    for (let usedDomain of usedDomains) {
        usedMap[usedDomain.id] = usedDomain
    }
    const domains: DomainWithConfigs[] = []
    for (let wsDomain of wsDomains) {
        const usedDomain = usedMap[wsDomain.domainId]
        const configs = usedDomain?.configs
        if (!configs) continue
        const domain: DomainWithConfigs = {
            ...wsDomain, configs, domain: usedDomain.domain
        }
        domains.push(domain)
    }
    return {
        domains, senders
    }
}

export const DeleteSenderEmail = async (userId: string, workspaceId: string, senderId: number) => {
    const member = await GetMyWorkspace(userId, workspaceId)
    if (!HasPermission(member, WorkspacePermission.UpdateWorkspace)) {
        throw new UnauthorizedError(ErrorMessage.UnableToAuthorize)
    }
    const service = new WorkspaceSenderService()
    await service.hardRemove({id: senderId, workspaceId})

    await RestoreWorkspaceLimitedFunctionality(member.workspace)

    return true
}

export const DeleteSenderDomain = async (userId: string, workspaceId: string, domainId: string) => {
    const member = await GetMyWorkspace(userId, workspaceId)
    if (!HasPermission(member, WorkspacePermission.UpdateWorkspace)) {
        throw new UnauthorizedError(ErrorMessage.UnableToAuthorize)
    }
    const service = new WorkspaceDomainService()
    const domain = await service.findOne({id: String(domainId), workspaceId})
    await service.hardRemove({id: domainId, workspaceId})

    if (domain) {
        const emailService = new WorkspaceSenderService()
        await emailService.hardRemove({
            workspaceId,
            workspaceDomainId: domainId
        })
    }
    await RestoreWorkspaceLimitedFunctionality(member.workspace)
    return true
}

export interface GetCheckoutSessionData {
    planId: string
    priceId: string
}

export const GetCheckoutSession = async (userId: string, workspaceId: string, data: GetCheckoutSessionData) => {
    const member = await GetMyWorkspace(userId, workspaceId)
    if (!HasPermission(member, WorkspacePermission.UpdateWorkspace)) {
        throw new UnauthorizedError(ErrorMessage.UnableToAuthorize)
    }
    const cycle = await CurrentBillingCycle(workspaceId)
    const w = await initStripeCustomer(member.workspace)
    const return_url = appUrl(`/${member.workspace.domain}/settings/billing`)
    const cancel_url = appUrl(`/${member.workspace.domain}/settings/plans`)


    let isUpgrade = true
    const currPlan = WorkspacePlanPriceIdMap[member.priceId]
    const futurePlan = WorkspacePlanPriceIdMap[data.priceId]

    if (!futurePlan) {
        throw new BadRequestError(ErrorMessage.PlanNotFound)
    }
    if (currPlan) {
        if (currPlan.id === futurePlan.id) {
            // the same plan, switch from monthly to annually or vice versa. start at end of current cycle
            isUpgrade = false
        } else if (currPlan.id === 'free') {
            // everything is upgrade
            isUpgrade = true
        } else if (currPlan.id === 'pro') {
            // everything is downgrade
            isUpgrade = false
        }
    }
    let startsAt: Date;
    if (!isUpgrade) {
        const currCycleEndsAt = new Date(cycle.endsAt)
        startsAt = datePlusDays(currCycleEndsAt, 1) // start a day after the current one ends
    }
    const amountInCents = futurePlan.commitments.annual.priceId === data.priceId ? futurePlan.commitments.annual.costInCents : futurePlan.commitments.monthToMonth.costInCents
    const {discountMonthsRemaining, affiliate} = await getAffiliateCalculateDiscountAndEarning(member.workspace, amountInCents)

    let couponId = ''
    if (affiliate && discountMonthsRemaining > 0) {
        const discountPercent = affiliate.referralDiscountPercent

        couponId = await GetOrCreateCoupon(discountPercent, discountMonthsRemaining)
    }

    const res = await createSubscriptionCheckoutSession({
        customer: w.stripeCustomerId,
        return_url,
        cancel_url,
        priceId: data.priceId,
        startsAt,
        couponId
    })
    const {session} = res

    return {
        session
    }
}

export const GetCustomerPortal = async (userId: string, workspaceId: string) => {
    const member = await GetMyWorkspace(userId, workspaceId)
    if (!HasPermission(member, WorkspacePermission.UpdateWorkspace)) {
        throw new UnauthorizedError(ErrorMessage.UnableToAuthorize)
    }
    const r = await initStripeCustomer(member.workspace)

    const return_url = appUrl(`/${member.workspace.domain}/settings/billing`)
    const res = await createCustomerPortal({customer: r.stripeCustomerId, return_url})
    const {portal} = res
    return {portal};
}

export const CancelFutureSubscription = async (userId: string, workspaceId: string) => {
    const member = await GetMyWorkspace(userId, workspaceId)
    if (!HasPermission(member, WorkspacePermission.UpdateWorkspace)) {
        throw new UnauthorizedError(ErrorMessage.UnableToAuthorize)
    }
    const billingCycle = await CurrentBillingCycle(workspaceId)
    if (billingCycle.futureStripeSubscriptionId) {
        await cancelSubscription(billingCycle.futureStripeSubscriptionId)
    }
}

export const cancelCurrentSubscription = async (userId: string, workspaceId: string) => {
    const member = await GetMyWorkspace(userId, workspaceId)
    if (!HasPermission(member, WorkspacePermission.UpdateWorkspace)) {
        throw new UnauthorizedError(ErrorMessage.UnableToAuthorize)
    }
    const billingCycle = await CurrentBillingCycle(workspaceId)
    if (billingCycle.stripeSubscriptionId) {
        await stopSubscriptionAutoRenew(billingCycle.stripeSubscriptionId)
    }
}

export const GetWorkspaceMemberSettings = async (userId: string, workspaceId: string) => {
    const member = await GetMyWorkspace(userId, workspaceId)
    if (!HasPermission(member, WorkspacePermission.ReadWorkspace)) {
        throw new UnauthorizedError(ErrorMessage.UnableToAuthorize)
    }
    const s = new WorkspaceMemberSettingService()
    let settings = await s.findOne({userId, workspaceId})
    if (!settings) settings = await s.insert({userId, workspaceId, settings: {}})
    return {
        settings
    }
}

export const GetWorkspaceUsage = async (userId: string, workspaceId: string): Promise<{
    stats: WorkspaceStats,
    billingCycle: BillingCycle,
    availableCreditInCents: number,
    subscription?: Subscription
}> => {
    const member = await GetMyWorkspace(userId, workspaceId)
    if (!HasPermission(member, WorkspacePermission.ReadWorkspace)) {
        throw new UnauthorizedError(ErrorMessage.UnableToAuthorize)
    }
    await RecalculateWorkspaceStats(workspaceId)
    return GetWorkspaceUsageNoAuth(member.workspace)
}
export const GetWorkspaceUsageNoAuth = async (workspace: Workspace): Promise<{
    stats: WorkspaceStats,
    billingCycle: BillingCycle,
    availableCreditInCents: number,
    subscription?: Subscription
}> => {
    const workspaceId = workspace.id
    const stats = await GetWorkspaceStats(workspaceId)
    const billingCycle = await CurrentBillingCycle(workspaceId)
    const {availableCreditInCents} = await ActiveCredits(workspaceId)


    let subscription: Subscription = undefined;
    if (billingCycle.stripeSubscriptionId) {
        subscription = await new SubscriptionService().findOne({stripeSubscriptionId: billingCycle.stripeSubscriptionId, workspaceId})
    }


    return {stats, billingCycle, availableCreditInCents, subscription}
}

export interface PurchaseWorkspaceCreditData {
    amountInCents: number
    currency?: string
}

export interface PurchaseWorkspaceCreditResponseData {
    credit: WorkspaceCredit
    checkoutUrl?: string
}

export const PurchaseWorkspaceCredit = async (userId: string, workspaceId: string, data: PurchaseWorkspaceCreditData): Promise<PurchaseWorkspaceCreditResponseData> => {
    const member = await GetMyWorkspace(userId, workspaceId)
    if (!HasPermission(member, WorkspacePermission.UpdateWorkspace)) {
        throw new UnauthorizedError(ErrorMessage.UnableToAuthorize)
    }
    let {amountInCents, currency} = data

    currency = currency || PaymentCurrency.USD
    if (currency !== PaymentCurrency.USD && currency !== PaymentCurrency.NGN) throw new InvalidParameterError("Invalid currency")

    const w = await initStripeCustomer(member.workspace)

    const calculateDiscountAndEarning = await getAffiliateCalculateDiscountAndEarning(member.workspace, amountInCents)
    const {amountToPayInCents, discountInCents, earningsInCents} = calculateDiscountAndEarning

    if (currency === PaymentCurrency.NGN) {
        return PurchaseWorkspaceCreditInNaira(userId, workspaceId, amountInCents, calculateDiscountAndEarning)
    }

    const {invoice} = await chargeCustomerViaInvoice({
        customer: w.stripeCustomerId,
        amountInCents: amountToPayInCents,
        description: "Credit Purchase"
    })
    const purchasedAt = new Date()
    const expiresAt = datePlusYears(purchasedAt, 1)
    // payment successful
    const s = new WorkspaceCreditService()
    const credit = await s.addCredit({
        workspaceId,
        purchasedAt,
        expiresAt,
        amountPaidInCents: amountToPayInCents,
        costInCents: amountInCents,
        creditAmountInCents: amountInCents,
        creditRemainingInCents: amountInCents,
        validFrom: purchasedAt,
        stripeInvoiceId: invoice.id,
        earningsInCents,
        status: PurchaseStatus.Settled,
        currency: currency,
        paymentProcessor: PaymentProcessor.Stripe,
        paymentProcessorReference: invoice.id,
        amountInLocalCurrency: amountToPayInCents
    })
    logInfo(`Credit purchase successful`, {workspaceId, userId, amountInCents, discountInCents, amountToPayInCents})
    await AddAffiliateEarningOnWorkspaceCreditPurchase(credit)
    return {
        credit
    }
}

const PurchaseWorkspaceCreditInNaira = async (userId: string, workspaceId: string, amountInCents: number, calculateDiscountAndEarning: AffiliateCalculateDiscountAndEarning): Promise<PurchaseWorkspaceCreditResponseData> => {
    const purchasedAt = new Date()
    const expiresAt = datePlusYears(purchasedAt, 1)

    const {amountToPayInCents, discountInCents, earningsInCents} = calculateDiscountAndEarning

    let meta: KeyValueStore = {}
    const rates = await getUSDNGNRate()
    const amountInLocalCurrency = Math.round(rates.rate * amountToPayInCents)

    meta['rate'] = rates

    const s = new WorkspaceCreditService()
    const credit = await s.addCredit({
        workspaceId,
        purchasedAt,
        expiresAt,
        amountPaidInCents: amountToPayInCents,
        costInCents: amountInCents,
        creditAmountInCents: amountInCents,
        creditRemainingInCents: amountInCents,
        validFrom: purchasedAt,
        stripeInvoiceId: null,
        earningsInCents,
        status: PurchaseStatus.Pending,
        currency: PaymentCurrency.NGN,
        paymentProcessor: PaymentProcessor.Paystack,
        paymentProcessorReference: null,
        amountInLocalCurrency,
        meta
    })

    const members = await getWorkspaceMemberNoCacheNoAuth(workspaceId)
    let ownerEmail = members.find(m => m.workspaceMember.role === WorkspaceMemberRole.Owner)?.user.email
    if (!ownerEmail) {
        ownerEmail = members.find(m => m.workspaceMember.role === WorkspaceMemberRole.Admin)?.user.email
    }

    const callback = apiUrl(`/v0/credit-purchases/${credit.id}/paystack-callback`);

    const transaction = await initializeTransaction(ownerEmail, amountInLocalCurrency, callback)

    await s.update({id: credit.id}, {paymentProcessorReference: transaction.reference})


    logInfo(`Credit purchase initiated`, {workspaceId, userId, amountInCents, discountInCents, amountToPayInCents, amountInLocalCurrency, paystackReference: transaction.reference})

    const checkoutUrl = transaction.authorization_url

    return {
        checkoutUrl, credit
    }
}

export const HandleNGNWorkspaceCreditPurchaseCallback = async (creditId: string, transactionRef: string): Promise<{ nextUrl: string }> => {
    const s = new WorkspaceCreditService()
    const credit = await s.findOne({id: Number(creditId)})
    if (!credit) throw new NotfoundError(ErrorMessage.EntityNotFound)

    const workspace = await new WorkspaceService().findOne({id: credit.workspaceId})

    let successLink = appUrl(`/${workspace.domain}/settings/billing`)

    const transaction = await getVerifiedTransaction(transactionRef)
    if (!transaction) {
        return {
            nextUrl: successLink + `?error=${encodeURIComponent("Transaction not found")}`
        }
    }
    if (transaction.status !== "success") {
        return {
            nextUrl: successLink + `?error=${encodeURIComponent("Transaction was not successful")}`
        }
    }
    if (Math.round(credit.amountInLocalCurrency) !== Math.round(transaction.amount)) {
        consoleLog(`Paystack Payment amount mismatch`, {amountInLocalCurrency: credit.amountInLocalCurrency, transactionAmount: transaction.amount})
        return {
            nextUrl: successLink + `?error=${encodeURIComponent("Payment amount mismatch")}`
        }
    }
    await s.update({id: credit.id}, {
        status: PurchaseStatus.Settled,
        purchasedAt: new Date()
    })

    return {
        nextUrl: successLink + `?success=true`
    }
}

export enum WorkspacePermission {
    ReadWorkspace = "readWorkspace",
    UpdateWorkspace = "updateWorkspace",
    DeleteWorkspace = "deleteWorkspace",
    CreateDatabase = "createDatabase",
    CreatePage = "createPage",
    ReadDatabase = "readDatabase",
    ManageWorkspaceSecret = "manageWorkspaceSecret",
}

export const HasPermission = (myWorkspace: MyWorkspace, permission: WorkspacePermission): boolean => {
    if (!myWorkspace || !myWorkspace.workspace) return false;

    const isAdmin = myWorkspace.workspaceMember.role === WorkspaceMemberRole.Admin || myWorkspace.workspaceMember?.role === WorkspaceMemberRole.SupportUser;
    const isOwner = myWorkspace.workspaceMember.role === WorkspaceMemberRole.Owner;
    const isMember = myWorkspace.workspaceMember.role === WorkspaceMemberRole.Member;
    // const isCollaborator = myWorkspace.workspaceMember?.role === WorkspaceMemberRole.Collaborator;
    // const isActiveMember = isAdmin || isOwner || isMember || isCollaborator

    if (permission === WorkspacePermission.ReadWorkspace) {
        return true;
    }
    if (permission === WorkspacePermission.UpdateWorkspace) {
        return isAdmin || isOwner
    }
    if (permission === WorkspacePermission.DeleteWorkspace) {
        return isOwner
    }
    // if (permission === WorkspacePermission.InviteWorkspaceMember) {
    //     return isAdmin || isOwner
    // }
    if (permission === WorkspacePermission.ManageWorkspaceSecret) {
        return isAdmin || isOwner
    }
    if (permission === WorkspacePermission.CreateDatabase) {
        return isOwner || isAdmin || isMember
    }
    if (permission === WorkspacePermission.CreatePage) {
        return isOwner || isAdmin || isMember
    }
    if (permission === WorkspacePermission.ReadDatabase) {
        return isOwner || isAdmin || isMember
    }
    return false
}

export const SaveActivities = async (workspaceId: string, databaseId: string, pageId: string, data: CreateActivity[]) => {
    const service = new ActivityService()

    const activities = await service.batchAdd(data)

    broadcastActivities(workspaceId, databaseId, pageId, activities)
}

export interface GetActivitiesData {
    databaseId: string
    objectType: ActivityObjectType
    objectId: string
}

export const GetActivities = async (userId: string, workspaceId: string, data: GetActivitiesData) => {
    const member = await GetMyWorkspace(userId, workspaceId)
    if (!HasPermission(member, WorkspacePermission.ReadWorkspace)) {
        throw new UnauthorizedError(ErrorMessage.UnableToAuthorize)
    }
    const service = new ActivityService()
    return await service.find(data)
}

export interface ModifyAddOnsData {
    addOnUsers: number
}

export const ModifyAddOns = async (userId: string, workspaceId: string, data: ModifyAddOnsData) => {
    if (data.addOnUsers !== 0 && !data.addOnUsers) throw new RequiredParameterError("addOnUsers")
    const users = Number(data.addOnUsers)
    if (Number.isNaN(users) || users < 0) throw new InvalidParameterError("Add on users must be 0 or more")
    const member = await GetMyWorkspace(userId, workspaceId)
    if (!HasPermission(member, WorkspacePermission.UpdateWorkspace)) {
        throw new UnauthorizedError(ErrorMessage.UnableToAuthorize)
    }
    const billingCycle = await CurrentBillingCycle(workspaceId)
    if (!billingCycle.stripeSubscriptionId) {
        throw new UnauthorizedError(ErrorMessage.SubscriptionNotActive)
    }
    return await ModifyAddOnUsersInSubscription(workspaceId, billingCycle.stripeSubscriptionId, users)
}

export interface GetNotificationsParams {
    page?: number
    perPage?: number
    filter?: 'unread' | 'read'
}

export const GetNotifications = async (userId: string, workspaceId: string, params: GetNotificationsParams) => {
    const s = new NotificationService();

    let offset = 0
    let page = Number(params.page)
    const perPage = params.perPage && !Number.isNaN(Number(params.perPage)) ? Number(params.perPage) : 20
    if (Number.isNaN(page) || page < 1) page = 1
    if (page > 1) {
        offset = (page - 1) * perPage
    }
    let notifications: Notification[] = []
    if (params.filter) {
        notifications = await s.find({userId, workspaceId, isSeen: params.filter === 'read'}, {}, perPage, offset)
    } else {
        notifications = await s.find({userId, workspaceId}, {}, perPage, offset)
    }

    return {notifications}
}

export interface UpdateNotificationData {
    id: string
    isSeen: boolean
}

export const UpdateNotification = async (userId: string, workspaceId: string, data: UpdateNotificationData) => {
    if (!data.id) throw new RequiredParameterError("id")
    if (data.isSeen === undefined) throw new RequiredParameterError("isSeen")

    const s = new NotificationService();

    const update: Partial<Notification> = {
        isSeen: data.isSeen
    }
    if (data.isSeen) update.seenAt = new Date()
    await s.update({id: data.id, workspaceId, userId}, update)
}

export interface CreateWorkspaceNotificationData {
    userId: string;
    title: string;
    message?: string;
    link?: string;
}

export const CreateWorkspaceNotification = async (userId: string, workspaceId: string, data: CreateWorkspaceNotificationData) => {
    if (!data.title) throw new RequiredParameterError("title")

    const member = await GetMyWorkspace(userId, workspaceId)
    if (!HasPermission(member, WorkspacePermission.ReadWorkspace)) {
        throw new UnauthorizedError(ErrorMessage.UnableToAuthorize)
    }

    const notificationData: CreateNotificationData = {
        userId: data.userId || userId,
        workspaceId,
        options: {
            title: data.title,
            message: data.message,
            link: data.link
        }
    }

    const { result } = await CreateNotifications([notificationData])
    const notification = result[0]

    return { notification }
}

export interface NotificationStats {
    unreadNotifications: number
    pendingReminders: number
}

export const GetNotificationStats = async (userId: string, workspaceId: string) => {
    const s = new NotificationService();

    const qB = s.getRepository()
        .createQueryBuilder()
        .select(qb => {
            return qb.select("COUNT(1)")
                .from(Notification, "n")
                .where({workspaceId, userId, isSeen: false})
        }, 'unreadNotifications')
        .addSelect(qb => {
            const brackets = new Brackets(qb => {
                qb.where("r.createdById=:userId", {userId});
                qb.orWhere("r.updatedById=:userId", {userId});
                qb.orWhere("r.assignedToUserIds LIKE :likeUserId", {likeUserId: `%${userId}%`});
            })
            return qb.select("COUNT(1)")
                .from(Reminder, "r")
                .where({workspaceId, isResolved: false})
                .andWhere(brackets)
        }, 'pendingReminders')


    const stats: NotificationStats = {
        pendingReminders: 0, unreadNotifications: 0
    }
    const rawResult = await qB.getRawMany()
    if (rawResult.length > 0) {
        stats.pendingReminders = Number(rawResult[0].pendingReminders)
        stats.unreadNotifications = Number(rawResult[0].unreadNotifications)
    }

    return {
        stats
    }
}

export interface AddWorkspaceSecretData extends Pick<CreateWorkspaceSecret, 'name' | 'value' | 'type'> {}

export const AddWorkspaceSecret = async (userId: string, workspaceId: string, data: AddWorkspaceSecretData): Promise<WorkspaceSecret> => {
    let name = (data.name || "").trim();
    let value = (data.value || "")
    let type = data.type

    if (!name) {
        throw new RequiredParameterError("name");
    }
    if (!validateUsername(name)) {
        throw new InvalidParameterError(`Name ${ErrorMessage.CanOnlyContainAZ09_}`);
    }
    if (data.type !== SecretType.Secret && data.type !== SecretType.Variable) {
        throw new InvalidParameterError("Type is invalid");
    }
    if (!value) {
        throw new RequiredParameterError("value");
    }
    name = name.toUpperCase();
    const member = await GetMyWorkspace(userId, workspaceId)
    if (!await HasPermission(member, WorkspacePermission.ManageWorkspaceSecret)) {
        throw new UnauthorizedError(ErrorMessage.UnableToAuthorize)
    }
    const service = new WorkspaceSecretService()

    const varData: CreateWorkspaceSecret = {
        workspaceId: workspaceId,
        createdById: userId,
        name,
        value,
        type
    }
    const secret = await service.addWorkspaceSecret(varData);
    if (type === SecretType.Secret) {
        secret.value = ''
    }

    return secret;
}

export interface GetAllWorkspaceSecretsParams {
    type?: SecretType
}

export const GetAllWorkspaceSecrets = async (userId: string, workspaceId: string, params: GetAllWorkspaceSecretsParams): Promise<WorkspaceSecret[]> => {
    const member = await GetMyWorkspace(userId, workspaceId)
    if (!await HasPermission(member, WorkspacePermission.ReadWorkspace)) {
        throw new UnauthorizedError(ErrorMessage.UnableToAuthorize)
    }
    const type = params.type || SecretType.Secret
    const service = new WorkspaceSecretService()

    if (type === SecretType.Secret) {
        return service.getWorkspaceSecrets(workspaceId)
    }
    return service.getWorkspaceVariables(workspaceId)
}

export interface UpdateWorkspaceSecretData extends AddWorkspaceSecretData {
    secretId: number
}

export const UpdateWorkspaceSecret = async (userId: string, workspaceId: string, data: UpdateWorkspaceSecretData) => {
    let name = (data.name || "").trim();
    let value = (data.value || "").trim();
    let type = data.type
    let secretId = data.secretId

    if (!name) {
        throw new RequiredParameterError("name");
    }
    if (!secretId) {
        throw new RequiredParameterError("secretId");
    }
    if (!validateUsername(name)) {
        throw new InvalidParameterError(`Name ${ErrorMessage.CanOnlyContainAZ09_}`);
    }
    if (type !== SecretType.Secret && type !== SecretType.Variable) {
        throw new InvalidParameterError("Type is invalid");
    }
    if (!value) {
        throw new RequiredParameterError("value");
    }
    name = name.toUpperCase();
    const member = await GetMyWorkspace(userId, workspaceId)
    if (!await HasPermission(member, WorkspacePermission.ManageWorkspaceSecret)) {
        throw new UnauthorizedError(ErrorMessage.UnableToAuthorize)
    }
    const service = new WorkspaceSecretService()
    const oldSecret = await service.findOne({type, id: secretId, workspaceId})
    if (!oldSecret) throw new NotfoundError(ErrorMessage.EntityNotFound)

    try {
        await service.update({id: secretId, workspaceId, type}, {name, value})
    } catch (err) {
        if (err.message.includes("Duplicate entry")) throw new UniqueConstraintError('name')
        throw new ServerProcessingError(err.message)
    }
    const secret: WorkspaceSecret = {...oldSecret, name, value}
    return secret
}

export interface DeleteWorkspaceSecretData {
    secretId: number
    type: SecretType
}

export const DeleteWorkspaceSecret = async (userId: string, workspaceId: string, data: DeleteWorkspaceSecretData): Promise<boolean> => {
    let type = data.type
    let secretId = data.secretId

    if (type !== SecretType.Secret && type !== SecretType.Variable) {
        throw new InvalidParameterError("Type is invalid");
    }
    if (!secretId) {
        throw new RequiredParameterError("secretId");
    }

    const member = await GetMyWorkspace(userId, workspaceId)
    if (!await HasPermission(member, WorkspacePermission.ManageWorkspaceSecret)) {
        throw new UnauthorizedError(ErrorMessage.UnableToAuthorize)
    }
    const service = new WorkspaceSecretService()

    await service.hardRemove({id: secretId, workspaceId, type})

    return true
}

export const RequestWorkspaceVariablesSecretsSubstitutionVars = async (workspaceId: string) => {
    const service = new WorkspaceSecretService()
    const secrets = await service.requestVariablesAndSecretsWithValue(workspaceId)

    const vars: SubstituteVarData = {}

    for (const secret of secrets) {
        const key = `@${secret.type.toLowerCase()}.${secret.name}`
        vars[key] = secret.value
    }
    return vars
}

export const ProvisionOpendashboardSiteSender = async (workspaceDomain: string = '') => {
    const s = new DomainService()
    const wDS = new WorkspaceDomainService()
    const wSS = new WorkspaceSenderService()
    let domain = await s.findOne({domain: 'opendashboard.site'})
    if (!domain) {
        domain = await s.insert({id: 0, domain: 'opendashboard.site', subdomain: 'opendbe', configs: [], addedByUserId: SystemParams.UserId, fullAuthorizedDomain: 'opendashboard.site', isVerified: true, verifiedAt: new Date()})
    }
    const workspaceMap: { [key: string]: { id: string, domain: string, name: string } } = {}

    const wS = new WorkspaceService()

    const qB1 = wS.getRepository().createQueryBuilder('w')
        .select()
    if (workspaceDomain) {
        qB1.where('w.domain=:domain', {domain: workspaceDomain})
    }
    const res1 = await qB1.getMany()
    for (const r of res1) {
        workspaceMap[r.id] = {id: r.id, domain: r.domain, name: r.name}
    }
    const qB = wSS.getRepository().createQueryBuilder('wSE')
        .select('wSE.workspaceId', 'workspaceId')
        .where({email: Like('%@opendashboard.site')})
        .withDeleted()
        .groupBy('wSE.workspaceId')

    const res = await qB.getRawMany()
    for (let re of res) {
        delete workspaceMap[re.workspaceId]
    }
    if (Object.keys(workspaceMap).length === 0) return

    const domainInfos: Pick<WorkspaceDomain, 'id' | 'workspaceId' | 'domainId' | 'addedByUserId' | 'isVerified' | 'verifiedAt'>[] = []
    const senderInfos: Pick<WorkspaceSenderEmail, 'email' | 'name' | 'addedByUserId' | 'workspaceId' | 'workspaceDomainId' | 'isVerified' | 'verifiedAt'>[] = []

    for (let entry of Object.entries(workspaceMap)) {
        const [workspaceId, {domain: wspDomain, name: wspName}] = entry

        const id = generateUUID()
        domainInfos.push({
            id,
            workspaceId,
            domainId: domain.id,
            addedByUserId: SystemParams.UserId,
            isVerified: true,
            verifiedAt: new Date()
        })
        senderInfos.push({
            email: `${wspDomain}@opendashboard.site`,
            name: `${wspName} via Opendashboard`,
            addedByUserId: SystemParams.UserId,
            workspaceId,
            workspaceDomainId: id,
            isVerified: true,
            verifiedAt: new Date()
        })
    }

    await wDS.batchAdd(domainInfos)
    await wSS.batchAdd(senderInfos)

    return Object.keys(workspaceMap).length
}


export const GetWorkspaceIntegrationConnections = async (userId: string, workspaceId: string, integration: string) => {
    const member = await GetMyWorkspace(userId, workspaceId);
    if (!member || member.workspaceMember.role === WorkspaceMemberRole.Collaborator) {
        throw new UnauthorizedError(ErrorMessage.AuthorizationNotFound);
    }
    return await getConnections({integration, workspaceId})
}

export interface DeleteWorkspaceIntegrationConnectionsData {
    connectionId: string
}

export const DeleteWorkspaceIntegrationConnections = async (userId: string, workspaceId: string, integration: string, data: DeleteWorkspaceIntegrationConnectionsData) => {
    const {connectionId} = data
    if (!connectionId) {
        throw new RequiredParameterError('connectionId')
    }

    const member = await GetMyWorkspace(userId, workspaceId);
    if (!member || !HasPermission(member, WorkspacePermission.UpdateWorkspace)) {
        throw new UnauthorizedError(ErrorMessage.AuthorizationNotFound);
    }
    await deleteConnection({integration, workspaceId, id: connectionId})

    return true
}

export interface SaveWorkspaceIntegrationConnectionData {
    connectionId?: string
    credentials: Record<string, string>
    name: string
}

export const SaveWorkspaceIntegrationConnection = async (userId: string, workspaceId: string, integration: string, data: SaveWorkspaceIntegrationConnectionData) => {
    const member = await GetMyWorkspace(userId, workspaceId);
    if (!member || member.workspaceMember.role === WorkspaceMemberRole.Collaborator) {
        throw new UnauthorizedError(ErrorMessage.AuthorizationNotFound);
    }
    let {connectionId, credentials: rawCredentials, name} = data

    const vars = await RequestWorkspaceVariablesSecretsSubstitutionVars(workspaceId)
    const credentials = substituteVarsInObjects(rawCredentials || {}, vars, 'curly') as Record<string, string>

    const coreApi = getCoreApi()
    let connection: WorkspaceIntegrationConnection;

    const isValid = await coreApi.verifyAuth({
        integration: {name: integration},
        credentials,
        saveConnection: async () => {
            connection = await saveConnection({
                integration,
                credentials,
                id: connectionId,
                name,
                workspaceId
            })
        },
    });
    if (!isValid) {
        throw new InvalidParameterError(ErrorMessage.UnableToSaveConnectionInvalidCredentials);
    }
    return connection
}

export interface StartWorkspaceIntegrationOAuth2RedirectData {
    clientId?: string
    clientSecret?: string
    name: string
    connectionId?: string
}

export const StartWorkspaceIntegrationOAuth2Redirect = async (userId: string, workspaceId: string, integration: string, data: StartWorkspaceIntegrationOAuth2RedirectData) => {
    const member = await GetMyWorkspace(userId, workspaceId);
    if (!member || member.workspaceMember.role === WorkspaceMemberRole.Collaborator) {
        throw new UnauthorizedError(ErrorMessage.AuthorizationNotFound);
    }
    let {connectionId, clientId, clientSecret, name} = data

    const coreApi = getCoreApi()
    const integrationInfo = await coreApi.getIntegration({name: integration});

    const encodedState = encodeState({
        connectionName: (name || integrationInfo.displayName).toString(),
        clientId: typeof clientId === 'string' ? clientId : undefined,
        clientSecret: typeof clientSecret === 'string' ? clientSecret : undefined,
        integrationParams: {name: integration},
        connectionId,
        metadata: {workspaceId}
    });
    const vars = await RequestWorkspaceVariablesSecretsSubstitutionVars(workspaceId)
    const {credentials, redirectUri} = await getOAuth2Configs(integration, {
        clientSecret: typeof clientSecret === 'string' ? substituteVars(clientSecret, vars, undefined, "curly") : '',
        clientId: typeof clientId === 'string' ? substituteVars(clientId, vars, undefined, "curly") : ''
    })

    const url = coreApi.getOAuth2RedirectURL({
        integration: integrationInfo,
        state: encodedState,
        credentials,
        redirectUri,
    });

    return {url}
}

export interface GetWorkspaceIntegrationOptionsData {
    propKey: string
    action?: string
    trigger?: string
    connectionId?: string
    propsValue?: Record<string, any>
}

export const GetWorkspaceIntegrationOptions = async (userId: string, workspaceId: string, integration: string, data: GetWorkspaceIntegrationOptionsData) => {
    const member = await GetMyWorkspace(userId, workspaceId);
    if (!member || member.workspaceMember.role === WorkspaceMemberRole.Collaborator) {
        throw new UnauthorizedError(ErrorMessage.AuthorizationNotFound);
    }
    let {connectionId, propKey, propsValue: rawProps, action, trigger} = data
    if (!propKey) {
        throw new RequiredParameterError('propKey')
    }
    if (!action && !trigger) {
        throw new RequiredParameterError('action and trigger')
    }
    const vars = await RequestWorkspaceVariablesSecretsSubstitutionVars(workspaceId)
    const propsValue = substituteVarsInObjects(rawProps || {}, vars, 'curly')

    const coreApi = getCoreApi()
    const webhookUrl = getWebhookURL('', integration, action ? 'action' : 'trigger')

    const params: RunStepParams<any> = {
        connectionId,
        integration: {name: integration},
        files: getFilesAPI(workspaceId),
        log: (args: any) => consoleLog(args),
        store: getStoreAPI(workspaceId, integration, null),
        connection: getConnectionAPI(workspaceId),
        propsValue,
        type: action ? 'action' : 'trigger',
        name: action || trigger,
        mode: "run",
        webhookUrl
    }
    const options = await coreApi.resolveDropdownOptions({
        ...params, key: propKey
    });
    return {options}
}

export interface ExecuteWorkspaceIntegrationActionData {
    name: string
    connectionId?: string
    propsValue?: Record<string, any>
    mode: 'run' | 'test',
    type: 'action' | 'trigger'
    payload?: WebhookTriggerPayload
}

export const ExecuteWorkspaceIntegrationAction = async (userId: string, workspaceId: string, integration: string, data: ExecuteWorkspaceIntegrationActionData, workflowId: string | null) => {
    const member = await GetMyWorkspace(userId, workspaceId);
    if (!member || member.workspaceMember.role === WorkspaceMemberRole.Collaborator) {
        throw new UnauthorizedError(ErrorMessage.AuthorizationNotFound);
    }
    return await ExecuteWorkspaceIntegrationActionNoAuth(workspaceId, integration, data, workflowId)
}

export const ExecuteWorkspaceIntegrationActionNoAuth = async (workspaceId: string, integration: string, data: ExecuteWorkspaceIntegrationActionData, workflowId: string | null) => {
    let {connectionId, propsValue: rawProps, name, mode, type, payload} = data

    if (!name) {
        throw new RequiredParameterError('name')
    }
    
    // For Apollo integration, always provide a connectionId
    if (integration === 'apollo' && !connectionId) {
        connectionId = workflowId || 'apollo-default';
    }
    
    const vars = await RequestWorkspaceVariablesSecretsSubstitutionVars(workspaceId)
    let propsValue = substituteVarsInObjects(rawProps || {}, vars, 'curly')
    
    // For Apollo integration, remove any api_key that might have been substituted
    if (integration === 'apollo' && propsValue) {
        
        const { api_key, ...cleanProps } = propsValue as any;
        if (api_key) {
            
        }
        propsValue = cleanProps;
        
    }

    const coreApi = getCoreApi()

    const webhookUrl = getWebhookURL(workflowId, integration, type === 'trigger' ? name : '')

    // Override getConnectionAPI for Apollo integration
    const connectionAPI = integration === 'apollo' ? {
        OAuth2Configs: async () => ({ credentials: { clientId: '', clientSecret: '' }, redirectUri: '' }),
        delete: async () => {},
        save: async () => {},
        get: async (integration: string, id: string) => {
            const apolloApiKey = process.env.APOLLO_API_KEY;
            if (!apolloApiKey) {
                throw new Error('APOLLO_API_KEY environment variable is not set');
            }
            
            return {
                id: 'apollo-default',
                name: 'Apollo Default Connection',
                integration: 'apollo',
                credentials: {
                    apiKey: apolloApiKey
                }
            };
        }
    } : getConnectionAPI(workspaceId);

    const params: RunStepParams<any> = {
        connectionId,
        integration: {name: integration},
        files: getFilesAPI(workspaceId),
        log: (args: any) => consoleLog(args),
        store: getStoreAPI(workspaceId, integration, workflowId),
        connection: connectionAPI,
        propsValue,
        type: type === 'action' ? 'action' : 'trigger',
        name,
        mode: mode === 'run' ? "run" : "test",
        payload,
        webhookUrl
    }
    const result = await coreApi.runStep(params);

    return {result}
}

export interface SendWorkspaceDirectEmailData {
    senderId: string;
    to: string;
    subject: string;
    cc?: string;
    bcc?: string;
    body: string;
}

export interface SendWorkspaceDirectEmailResponse {
    messageId: string;
    success: boolean;
}

export const SendWorkspaceDirectEmail = async (userId: string, workspaceId: string, data: SendWorkspaceDirectEmailData): Promise<SendWorkspaceDirectEmailResponse> => {
    const member = await GetMyWorkspace(userId, workspaceId)
    if (!HasPermission(member, WorkspacePermission.ReadWorkspace)) {
        throw new UnauthorizedError(ErrorMessage.UnableToAuthorize)
    }

    const senderId = (data.senderId || '').trim()
    const subject = (data.subject || '').trim()
    const to = (data.to || '').trim()
    const body = (data.body || '').trim()

    if (!senderId) throw new RequiredParameterError("senderId")
    if (!subject) throw new RequiredParameterError("subject")
    if (!to) throw new RequiredParameterError("to")
    if (!body) throw new RequiredParameterError("body")

    if (!validateEmail(to)) {
        throw new InvalidParameterError(`'${to}' is not a valid email address`)
    }

    const senderService = new WorkspaceSenderService()
    const sender = await senderService.findOne({ id: Number(senderId), workspaceId })
    if (!sender) {
        throw new NotfoundError("Sender not found")
    }
    if (!sender.isVerified) {
        throw new BadRequestError("Sender email is not verified")
    }

    const billable: Billable = {
        workspaceId,
        emailSent: 1,
        aiGeneration: 0,
        enrichment: 0,
        workflowTask: 0
    }
    await billWorkspaceCredit(billable)

    const emailTo: EmailUser = {
        email: to,
        name: ''
    }

    const emailFrom: SMTPEmailUser = {
        address: sender.email,
        name: sender.name
    }


    const cc = data.cc ? data.cc.split(',').map(email => email.trim()).filter(email => validateEmail(email)) : [];
    const bcc = data.bcc ? data.bcc.split(',').map(email => email.trim()).filter(email => validateEmail(email)) : [];


    const messageId = await SendEmailWithContent(
        emailTo,
        subject,
        body,
        null,
        null,
        true,
        emailFrom,
        sendgridTransport,
        undefined,
        [],
        true,
        cc,
        bcc
    )

    return { messageId, success: true  }
}
