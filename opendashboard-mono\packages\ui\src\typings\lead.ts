export interface Lead {
    id: string;
    workspaceId: string;
    apolloId?: string;
    type: 'person' | 'company';
    source: 'apollo' | 'manual' | 'import';
    name: string;
    email?: string;
    companyDomain?: string;
    isUnlocked: boolean;
    lastEnrichedAt?: Date;
    createdAt: Date;
    updatedAt: Date;
    deletedAt?: Date;
    createdById?: string;
    updatedById?: string;
    normalizedData: NormalizedLeadData;
    apolloData?: ApolloPersonData | ApolloCompanyData;
    searchHashes?: string[];
    meta?: LeadMeta;
}

export interface ApolloPersonData {
    id: string;
    first_name?: string;
    last_name?: string;
    name?: string;
    linkedin_url?: string;
    title?: string;
    email?: string;
    phone?: string;
    organization?: {
        id?: string;
        name?: string;
        website_url?: string;
        linkedin_url?: string;
        primary_domain?: string;
        industry?: string;
        keywords?: string[];
        estimated_num_employees?: number;
    };
    employment_history?: Array<{
        company_name?: string;
        title?: string;
        start_date?: string;
        end_date?: string;
        is_current?: boolean;
    }>;
    photo_url?: string;
    twitter_url?: string;
    github_url?: string;
    facebook_url?: string;
    extrapolated_email_confidence?: number;
    headline?: string;
    country?: string;
    state?: string;
    city?: string;
    [key: string]: string | number | boolean | string[] | number[] | Record<string, string | number | boolean> | Array<{
        company_name?: string;
        title?: string;
        start_date?: string;
        end_date?: string;
        is_current?: boolean;
    }> | {
        id?: string;
        name?: string;
        website_url?: string;
        linkedin_url?: string;
        primary_domain?: string;
        industry?: string;
        keywords?: string[];
        estimated_num_employees?: number;
    };
}

export interface ApolloCompanyData {
    id: string;
    name: string;
    website_url?: string;
    linkedin_url?: string;
    primary_domain?: string;
    industry?: string;
    keywords?: string[];
    estimated_num_employees?: number;
    retail_location_count?: number;
    stage?: string;
    short_description?: string;
    founded_year?: number;
    publicly_traded_symbol?: string;
    publicly_traded_exchange?: string;
    logo_url?: string;
    crunchbase_url?: string;
    primary_phone?: {
        number?: string;
        source?: string;
    };
    languages?: string[];
    alexa_ranking?: number;
    phone?: string;
    technologies?: string[];
    annual_revenue?: string;
    total_funding?: number;
    latest_funding_round_date?: string;
    latest_funding_stage?: string;
    seo_description?: string;
    technology_names?: string[];
    current_technologies?: Array<{
        name?: string;
        category?: string;
        description?: string;
    }>;
    account_id?: string;
    [key: string]: string | number | boolean | string[] | number[] | Record<string, string | number | boolean> | Array<{
        name?: string;
        category?: string;
        description?: string;
    }> | {
        number?: string;
        source?: string;
    };
}

export interface NormalizedLeadData {
    id: string;
    name: string;
    email?: string;
    phone?: string;
    jobTitle?: string;
    company?: string;
    companyDomain?: string;
    linkedinUrl?: string;
    photoUrl?: string;
    location?: {
        country?: string;
        state?: string;
        city?: string;
    };
    isEmailVisible: boolean;
    isPhoneVisible: boolean;
    confidence?: number;
}

export interface LeadVoteFeedback {
    vote: 'up' | 'down';
    feedback: string;
    feedbackType: 'predefined' | 'custom';
    createdAt: Date;
    createdBy: string;
}

export interface LeadMeta {
    votes?: LeadVoteFeedback[];
    [key: string]: string | number | boolean | string[] | number[] | LeadVoteFeedback[] | undefined;
}

export interface PersonFilters {
    jobTitles?: string[];
    managementLevel?: string[];
    jobFunction?: string[];
    location?: string[];
    seniority?: string[];
    departments?: string[];
    skills?: string[];
}

export interface CompanyFilters {
    industry?: string[];
    industries?: string[]; // Add support for both field names
    companySize?: string[];
    companyType?: string[];
    location?: string[];
    technologies?: string[];
    keywords?: string[];
    revenue?: {
        min?: number;
        max?: number;
    };
    employees?: {
        min?: number;
        max?: number;
    };
    foundedYear?: {
        min?: number;
        max?: number;
    };
}

export interface SignalFilters {
    recentlyPromoted?: boolean;
    formerChampionChangedJobs?: boolean;
    highBuyingIntent?: boolean;
    rapidGrowth?: boolean;
    openedEmails?: boolean;
    newRole?: boolean;
    jobChanges?: boolean;
    companyGrowth?: boolean;
    newTechnologies?: boolean;
    fundingEvents?: boolean;
}

export interface SearchFilters {
    person?: PersonFilters;
    company?: CompanyFilters;
    signals?: SignalFilters;
    customFilters?: Record<string, string | number | boolean | string[] | number[]>;
}

export interface SearchPagination {
    page: number;
    limit: number;
    totalCount?: number;
    hasNextPage?: boolean;
}

export interface SearchResultMetadata {
    apolloRequestId?: string;
    apolloCreditsUsed?: number;
    processingTimeMs?: number;
    resultQuality?: 'high' | 'medium' | 'low';
    dataFreshness?: Date;
    totalPagesAvailable?: number; // Track how many pages are currently cached
}

export interface SearchLeadsRequest {
    filters: SearchFilters;
    pagination?: SearchPagination;
    excludeMyLeads?: boolean;
    customFilters?: Record<string, any>;
}

export interface SearchLeadsResponse {
    leads: Lead[];
    totalCount: number;
    hasNextPage: boolean;
    searchId: string;
    filters: SearchFilters;
    metadata: SearchResultMetadata;
}

export interface UnlockLeadResponse {
    lead: Lead;
    unlock: {
        id: string;
        unlockedAt: Date;
        unlockType: string;
        creditsUsed: number;
        isSuccessful: boolean;
    };
    alreadyUnlocked: boolean;
}

export interface SavedSearch {
    id: string;
    workspaceId: string;
    name: string;
    description?: string;
    filters: SearchFilters;
    searchHash: string;
    totalCount: number;
    numberLoaded: number;
    pageLoaded: number;
    resultIds: string[];
    isSaved: boolean;
    lastExecutedAt: Date;
    createdAt: Date;
    updatedAt: Date;
    expiresAt?: Date;
    isActive: boolean;
    createdById: string;
    updatedById?: string;
    metadata?: SearchResultMetadata;
}

export interface VoteFeedbackOptions {
    upvote: string[];
    downvote: string[];
}